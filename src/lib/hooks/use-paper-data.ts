// React Hooks for Paper Data Management
// Custom hooks to integrate paper storage with React components

import { useState, useEffect, useCallback } from 'react';
import { PaperOption } from '../paper-data';
import { paperStorage } from '../paper-storage';

type StorageCategory = 'Inner Text' | 'Cover' | 'Endpapers';

// Hook for managing paper data for a specific category
export const usePaperData = (category: StorageCategory) => {
  const [papers, setPapers] = useState<PaperOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load papers from storage
  const loadPapers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const loadedPapers = paperStorage.loadPapers(category);
      setPapers(loadedPapers);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load papers');
    } finally {
      setLoading(false);
    }
  }, [category]);

  // Add a new paper
  const addPaper = useCallback(async (paperData: Omit<PaperOption, 'id' | 'category'>) => {
    try {
      const newPaper = paperStorage.addPaper(category, paperData);
      if (newPaper) {
        setPapers(prev => [...prev, newPaper]);
        return newPaper;
      } else {
        throw new Error('Failed to add paper');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add paper');
      return null;
    }
  }, [category]);

  // Update an existing paper
  const updatePaper = useCallback(async (paperId: string, updates: Partial<PaperOption>) => {
    try {
      const updatedPaper = paperStorage.updatePaper(category, paperId, updates);
      if (updatedPaper) {
        setPapers(prev => prev.map(paper => 
          paper.id === paperId ? updatedPaper : paper
        ));
        return updatedPaper;
      } else {
        throw new Error('Failed to update paper');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update paper');
      return null;
    }
  }, [category]);

  // Delete a paper
  const deletePaper = useCallback(async (paperId: string) => {
    try {
      const success = paperStorage.deletePaper(category, paperId);
      if (success) {
        setPapers(prev => prev.filter(paper => paper.id !== paperId));
        return true;
      } else {
        throw new Error('Failed to delete paper');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete paper');
      return false;
    }
  }, [category]);

  // Search papers
  const searchPapers = useCallback((searchTerm: string) => {
    return paperStorage.searchPapers(category, searchTerm);
  }, [category]);

  // Filter papers
  const filterPapers = useCallback((filters: {
    paperType?: string;
    minWeight?: number;
    maxWeight?: number;
    grainDirection?: string;
    hasRollPricing?: boolean;
  }) => {
    return paperStorage.filterPapers(category, filters);
  }, [category]);

  // Reset to default data
  const resetToDefaults = useCallback(async () => {
    try {
      const success = paperStorage.resetCategory(category);
      if (success) {
        await loadPapers();
        return true;
      } else {
        throw new Error('Failed to reset category');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reset category');
      return false;
    }
  }, [category, loadPapers]);

  // Load papers on mount
  useEffect(() => {
    loadPapers();
  }, [loadPapers]);

  return {
    papers,
    loading,
    error,
    addPaper,
    updatePaper,
    deletePaper,
    searchPapers,
    filterPapers,
    resetToDefaults,
    reload: loadPapers,
    clearError: () => setError(null)
  };
};

// Hook for managing all paper categories
export const useAllPaperData = () => {
  const [allData, setAllData] = useState<{ [key: string]: PaperOption[] }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadAllData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = paperStorage.exportAllData();
      setAllData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load all data');
    } finally {
      setLoading(false);
    }
  }, []);

  const importData = useCallback(async (data: { [key: string]: PaperOption[] }) => {
    try {
      const success = paperStorage.importData(data);
      if (success) {
        await loadAllData();
        return true;
      } else {
        throw new Error('Failed to import data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to import data');
      return false;
    }
  }, [loadAllData]);

  const resetAllCategories = useCallback(async () => {
    try {
      const success = paperStorage.resetAllCategories();
      if (success) {
        await loadAllData();
        return true;
      } else {
        throw new Error('Failed to reset all categories');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reset all categories');
      return false;
    }
  }, [loadAllData]);

  const clearAllData = useCallback(async () => {
    try {
      const success = paperStorage.clearAllData();
      if (success) {
        setAllData({});
        return true;
      } else {
        throw new Error('Failed to clear all data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear all data');
      return false;
    }
  }, []);

  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  return {
    allData,
    loading,
    error,
    importData,
    resetAllCategories,
    clearAllData,
    reload: loadAllData,
    clearError: () => setError(null)
  };
};

// Hook for paper statistics
export const usePaperStats = () => {
  const [stats, setStats] = useState({
    totalPapers: 0,
    categoryCounts: {} as { [key: string]: number },
    paperTypeCounts: {} as { [key: string]: number },
    averageWeight: 0,
    weightRange: { min: 0, max: 0 },
    lastUpdated: null as Date | null
  });

  const calculateStats = useCallback(() => {
    const allData = paperStorage.exportAllData();
    const allPapers = Object.values(allData).flat();
    
    const categoryCounts: { [key: string]: number } = {};
    const paperTypeCounts: { [key: string]: number } = {};
    
    allPapers.forEach(paper => {
      categoryCounts[paper.category] = (categoryCounts[paper.category] || 0) + 1;
      paperTypeCounts[paper.paperType] = (paperTypeCounts[paper.paperType] || 0) + 1;
    });
    
    const weights = allPapers.map(paper => paper.weight);
    const averageWeight = weights.length > 0 ? weights.reduce((sum, w) => sum + w, 0) / weights.length : 0;
    const minWeight = weights.length > 0 ? Math.min(...weights) : 0;
    const maxWeight = weights.length > 0 ? Math.max(...weights) : 0;
    
    setStats({
      totalPapers: allPapers.length,
      categoryCounts,
      paperTypeCounts,
      averageWeight: Math.round(averageWeight),
      weightRange: { min: minWeight, max: maxWeight },
      lastUpdated: paperStorage.getLastUpdated()
    });
  }, []);

  useEffect(() => {
    calculateStats();
    
    // Recalculate stats when localStorage changes
    const handleStorageChange = () => {
      calculateStats();
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [calculateStats]);

  return {
    stats,
    refresh: calculateStats
  };
};

// Hook for storage information
export const useStorageInfo = () => {
  const [storageInfo, setStorageInfo] = useState<{ [key: string]: number } | null>(null);
  
  const refreshStorageInfo = useCallback(() => {
    const info = paperStorage.getStorageInfo();
    setStorageInfo(info);
  }, []);
  
  useEffect(() => {
    refreshStorageInfo();
  }, [refreshStorageInfo]);
  
  return {
    storageInfo,
    refresh: refreshStorageInfo
  };
};