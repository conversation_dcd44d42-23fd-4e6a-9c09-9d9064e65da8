// Paper Options Database
// This file contains the structured paper options data for the paper cost estimator

export interface PaperOption {
  id: string;
  name: string;
  paperType: string; // Source (Pre-Cut, Roll)
  weight: number; // GSM (g/m²)
  color: string; // Sheet H (mm)
  finish: string; // Sheet W (mm)
  printingSide: string; // Grain direction (Height, Width)
  quantity: number; // Caliper (µm)
  notes: string; // Cost/Ream ($)
  costTon?: number; // Cost/Ton ($)
  category: 'Inner Text' | 'Cover' | 'Endpapers';
}

export interface PaperCategory {
  category: string;
  papers: PaperOption[];
}

// Inner Text Papers
const innerTextPapers: PaperOption[] = [
  {
    id: 'inner-1',
    name: 'Halved 31 x 43"',
    paperType: 'Pre-Cut',
    color: '546.1', // Sheet H (mm)
    finish: '787.4', // Sheet W (mm)
    printingSide: 'Height', // Grain
    quantity: 100, // Caliper (µm)
    notes: '50.00', // Cost/Ream ($)
    weight: 80, // GSM (g/m²)
    costTon: undefined, // Cost/Ton ($) - not provided
    category: 'Inner Text'
  },
  {
    id: 'inner-2',
    name: 'Halved 35 x 47"',
    paperType: 'Pre-Cut',
    color: '596.9',
    finish: '889.0',
    printingSide: 'Height',
    quantity: 100,
    notes: '22.50',
    weight: 80,
    costTon: undefined,
    category: 'Inner Text'
  },
  {
    id: 'inner-3',
    name: 'Quarter 35 x 47"',
    paperType: 'Pre-Cut',
    color: '444.5',
    finish: '596.9',
    printingSide: 'Width',
    quantity: 100,
    notes: '11.25',
    weight: 80,
    costTon: undefined,
    category: 'Inner Text'
  },
  {
    id: 'inner-4',
    name: 'Special 25 x 38"',
    paperType: 'Pre-Cut',
    color: '635.0',
    finish: '965.2',
    printingSide: 'Width',
    quantity: 100,
    notes: '40.00',
    weight: 80,
    costTon: undefined,
    category: 'Inner Text'
  },
  {
    id: 'inner-5',
    name: 'Custom 25" Roll',
    paperType: 'Roll',
    color: '-', // No height for roll
    finish: '635.0',
    printingSide: 'Height',
    quantity: 100,
    notes: '-', // No cost per ream for roll
    weight: 80,
    costTon: 1200.00,
    category: 'Inner Text'
  },
  {
    id: 'inner-6',
    name: 'Custom 31" Roll',
    paperType: 'Roll',
    color: '-',
    finish: '787.4',
    printingSide: 'Height',
    quantity: 100,
    notes: '-',
    weight: 80,
    costTon: 1200.00,
    category: 'Inner Text'
  },
  {
    id: 'inner-7',
    name: 'Custom 35" Roll',
    paperType: 'Roll',
    color: '-',
    finish: '889.0',
    printingSide: 'Height',
    quantity: 100,
    notes: '-',
    weight: 80,
    costTon: 1200.00,
    category: 'Inner Text'
  },
  {
    id: 'inner-8',
    name: 'Custom 38" Roll',
    paperType: 'Roll',
    color: '-',
    finish: '965.2',
    printingSide: 'Height',
    quantity: 100,
    notes: '-',
    weight: 80,
    costTon: 1200.00,
    category: 'Inner Text'
  }
];

// Cover Papers
const coverPapers: PaperOption[] = [
  {
    id: 'cover-1',
    name: 'Art Card 250gsm',
    paperType: 'Pre-Cut',
    color: '635', // Sheet H (mm)
    finish: '965', // Sheet W (mm)
    printingSide: 'Height', // Grain
    quantity: 280, // Caliper (µm)
    notes: '120.00', // Cost/Ream ($)
    weight: 250, // GSM (g/m²)
    costTon: undefined, // Cost/Ton ($) - not provided
    category: 'Cover'
  },
  {
    id: 'cover-2',
    name: 'Coated Board 300gsm',
    paperType: 'Pre-Cut',
    color: '700',
    finish: '1000',
    printingSide: 'Width',
    quantity: 350,
    notes: '150.00',
    weight: 300,
    costTon: undefined,
    category: 'Cover'
  }
];

// Endpaper Papers
const endpaperPapers: PaperOption[] = [
  {
    id: 'endpaper-1',
    name: 'Woodfree 120gsm',
    paperType: 'Pre-Cut',
    color: '787.4', // Sheet H (mm)
    finish: '1092.2', // Sheet W (mm)
    printingSide: 'Height', // Grain
    quantity: 140, // Caliper (µm)
    notes: '65.00', // Cost/Ream ($)
    weight: 120, // GSM (g/m²)
    costTon: undefined, // Cost/Ton ($) - not provided
    category: 'Endpapers'
  },
  {
    id: 'endpaper-2',
    name: 'Specialty Endpaper 140gsm',
    paperType: 'Pre-Cut',
    color: '650',
    finish: '900',
    printingSide: 'Width',
    quantity: 160,
    notes: '80.00',
    weight: 140,
    costTon: undefined,
    category: 'Endpapers'
  }
];

// Combined paper database
export const paperDatabase: PaperCategory[] = [
  {
    category: 'Inner Text',
    papers: innerTextPapers
  },
  {
    category: 'Cover',
    papers: coverPapers
  },
  {
    category: 'Endpapers',
    papers: endpaperPapers
  }
];

// Utility functions for paper data management
export const paperDataUtils = {
  // Get all papers from all categories
  getAllPapers: (): PaperOption[] => {
    return paperDatabase.flatMap(category => category.papers);
  },

  // Get papers by category
  getPapersByCategory: (category: string): PaperOption[] => {
    const categoryData = paperDatabase.find(cat => cat.category === category);
    return categoryData ? categoryData.papers : [];
  },

  // Get paper by ID
  getPaperById: (id: string): PaperOption | undefined => {
    return paperDataUtils.getAllPapers().find(paper => paper.id === id);
  },

  // Get all available categories
  getCategories: (): string[] => {
    return paperDatabase.map(category => category.category);
  },

  // Get all paper types (sources)
  getPaperTypes: (): string[] => {
    const types = new Set(paperDataUtils.getAllPapers().map(paper => paper.paperType));
    return Array.from(types);
  },

  // Get all grain directions
  getGrainDirections: (): string[] => {
    const grains = new Set(paperDataUtils.getAllPapers().map(paper => paper.printingSide));
    return Array.from(grains);
  },

  // Search papers by name
  searchPapersByName: (searchTerm: string): PaperOption[] => {
    const term = searchTerm.toLowerCase();
    return paperDataUtils.getAllPapers().filter(paper => 
      paper.name.toLowerCase().includes(term)
    );
  },

  // Filter papers by criteria
  filterPapers: (criteria: {
    category?: string;
    paperType?: string;
    minWeight?: number;
    maxWeight?: number;
    grainDirection?: string;
  }): PaperOption[] => {
    let papers = paperDataUtils.getAllPapers();

    if (criteria.category) {
      papers = papers.filter(paper => paper.category === criteria.category);
    }

    if (criteria.paperType) {
      papers = papers.filter(paper => paper.paperType === criteria.paperType);
    }

    if (criteria.minWeight !== undefined) {
      papers = papers.filter(paper => paper.weight >= criteria.minWeight!);
    }

    if (criteria.maxWeight !== undefined) {
      papers = papers.filter(paper => paper.weight <= criteria.maxWeight!);
    }

    if (criteria.grainDirection) {
      papers = papers.filter(paper => paper.printingSide === criteria.grainDirection);
    }

    return papers;
  },

  // Add new paper to a category
  addPaper: (categoryName: string, paper: Omit<PaperOption, 'id'>): PaperOption => {
    const newPaper: PaperOption = {
      ...paper,
      id: `${categoryName.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
      category: categoryName as 'Inner Text' | 'Cover' | 'Endpapers'
    };

    const category = paperDatabase.find(cat => cat.category === categoryName);
    if (category) {
      category.papers.push(newPaper);
    }

    return newPaper;
  },

  // Update paper
  updatePaper: (id: string, updates: Partial<PaperOption>): PaperOption | null => {
    for (const category of paperDatabase) {
      const paperIndex = category.papers.findIndex(paper => paper.id === id);
      if (paperIndex !== -1) {
        category.papers[paperIndex] = { ...category.papers[paperIndex], ...updates };
        return category.papers[paperIndex];
      }
    }
    return null;
  },

  // Delete paper
  deletePaper: (id: string): boolean => {
    for (const category of paperDatabase) {
      const paperIndex = category.papers.findIndex(paper => paper.id === id);
      if (paperIndex !== -1) {
        category.papers.splice(paperIndex, 1);
        return true;
      }
    }
    return false;
  }
};

// Export individual categories for convenience
export { innerTextPapers, coverPapers, endpaperPapers };