// Paper Storage Service
// This service provides CRUD operations and persistence for paper options data

import { PaperOption, PaperCategory, paperDatabase, paperDataUtils } from './paper-data';

// Storage keys for localStorage
const STORAGE_KEYS = {
  INNER_TEXT: 'paper_options_inner_text',
  COVER: 'paper_options_cover',
  ENDPAPERS: 'paper_options_endpapers',
  LAST_UPDATED: 'paper_options_last_updated'
} as const;

// Type for storage operations
type StorageCategory = 'Inner Text' | 'Cover' | 'Endpapers';

// Paper Storage Service Class
export class PaperStorageService {
  private static instance: PaperStorageService;
  private isClient: boolean;

  private constructor() {
    this.isClient = typeof window !== 'undefined';
  }

  // Singleton pattern
  public static getInstance(): PaperStorageService {
    if (!PaperStorageService.instance) {
      PaperStorageService.instance = new PaperStorageService();
    }
    return PaperStorageService.instance;
  }

  // Get storage key for category
  private getStorageKey(category: StorageCategory): string {
    switch (category) {
      case 'Inner Text':
        return STORAGE_KEYS.INNER_TEXT;
      case 'Cover':
        return STORAGE_KEYS.COVER;
      case 'Endpapers':
        return STORAGE_KEYS.ENDPAPERS;
      default:
        throw new Error(`Unknown category: ${category}`);
    }
  }

  // Load papers from localStorage or return default data
  public loadPapers(category: StorageCategory): PaperOption[] {
    if (!this.isClient) {
      return paperDataUtils.getPapersByCategory(category);
    }

    try {
      const storageKey = this.getStorageKey(category);
      const stored = localStorage.getItem(storageKey);
      
      if (stored) {
        const papers: PaperOption[] = JSON.parse(stored);
        return papers.map(paper => ({ ...paper, category }));
      }
    } catch (error) {
      console.warn(`Failed to load papers for ${category}:`, error);
    }

    // Return default data if no stored data or error
    return paperDataUtils.getPapersByCategory(category);
  }

  // Save papers to localStorage
  public savePapers(category: StorageCategory, papers: PaperOption[]): boolean {
    if (!this.isClient) {
      console.warn('Cannot save to localStorage on server side');
      return false;
    }

    try {
      const storageKey = this.getStorageKey(category);
      localStorage.setItem(storageKey, JSON.stringify(papers));
      localStorage.setItem(STORAGE_KEYS.LAST_UPDATED, new Date().toISOString());
      return true;
    } catch (error) {
      console.error(`Failed to save papers for ${category}:`, error);
      return false;
    }
  }

  // Add a new paper
  public addPaper(category: StorageCategory, paperData: Omit<PaperOption, 'id' | 'category'>): PaperOption | null {
    try {
      const papers = this.loadPapers(category);
      const newPaper: PaperOption = {
        ...paperData,
        id: `${category.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
        category
      };
      
      const updatedPapers = [...papers, newPaper];
      
      if (this.savePapers(category, updatedPapers)) {
        return newPaper;
      }
    } catch (error) {
      console.error(`Failed to add paper to ${category}:`, error);
    }
    return null;
  }

  // Update an existing paper
  public updatePaper(category: StorageCategory, paperId: string, updates: Partial<PaperOption>): PaperOption | null {
    try {
      const papers = this.loadPapers(category);
      const paperIndex = papers.findIndex(paper => paper.id === paperId);
      
      if (paperIndex === -1) {
        console.warn(`Paper with ID ${paperId} not found in ${category}`);
        return null;
      }
      
      const updatedPaper = { ...papers[paperIndex], ...updates, category };
      papers[paperIndex] = updatedPaper;
      
      if (this.savePapers(category, papers)) {
        return updatedPaper;
      }
    } catch (error) {
      console.error(`Failed to update paper ${paperId} in ${category}:`, error);
    }
    return null;
  }

  // Delete a paper
  public deletePaper(category: StorageCategory, paperId: string): boolean {
    try {
      const papers = this.loadPapers(category);
      const filteredPapers = papers.filter(paper => paper.id !== paperId);
      
      if (filteredPapers.length === papers.length) {
        console.warn(`Paper with ID ${paperId} not found in ${category}`);
        return false;
      }
      
      return this.savePapers(category, filteredPapers);
    } catch (error) {
      console.error(`Failed to delete paper ${paperId} from ${category}:`, error);
      return false;
    }
  }

  // Get a specific paper by ID
  public getPaper(category: StorageCategory, paperId: string): PaperOption | null {
    const papers = this.loadPapers(category);
    return papers.find(paper => paper.id === paperId) || null;
  }

  // Search papers within a category
  public searchPapers(category: StorageCategory, searchTerm: string): PaperOption[] {
    const papers = this.loadPapers(category);
    const term = searchTerm.toLowerCase();
    
    return papers.filter(paper => 
      paper.name.toLowerCase().includes(term) ||
      paper.paperType.toLowerCase().includes(term) ||
      paper.printingSide.toLowerCase().includes(term)
    );
  }

  // Filter papers within a category
  public filterPapers(category: StorageCategory, filters: {
    paperType?: string;
    minWeight?: number;
    maxWeight?: number;
    grainDirection?: string;
    hasRollPricing?: boolean;
  }): PaperOption[] {
    const papers = this.loadPapers(category);
    
    return papers.filter(paper => {
      if (filters.paperType && paper.paperType !== filters.paperType) {
        return false;
      }
      
      if (filters.minWeight !== undefined && paper.weight < filters.minWeight) {
        return false;
      }
      
      if (filters.maxWeight !== undefined && paper.weight > filters.maxWeight) {
        return false;
      }
      
      if (filters.grainDirection && paper.printingSide !== filters.grainDirection) {
        return false;
      }
      
      if (filters.hasRollPricing !== undefined) {
        const hasRollPricing = paper.costTon !== undefined;
        if (filters.hasRollPricing !== hasRollPricing) {
          return false;
        }
      }
      
      return true;
    });
  }

  // Reset category to default data
  public resetCategory(category: StorageCategory): boolean {
    const defaultPapers = paperDataUtils.getPapersByCategory(category);
    return this.savePapers(category, defaultPapers);
  }

  // Reset all categories to default data
  public resetAllCategories(): boolean {
    const categories: StorageCategory[] = ['Inner Text', 'Cover', 'Endpapers'];
    let allSuccess = true;
    
    categories.forEach(category => {
      if (!this.resetCategory(category)) {
        allSuccess = false;
      }
    });
    
    return allSuccess;
  }

  // Export all data
  public exportAllData(): { [key: string]: PaperOption[] } {
    return {
      'Inner Text': this.loadPapers('Inner Text'),
      'Cover': this.loadPapers('Cover'),
      'Endpapers': this.loadPapers('Endpapers')
    };
  }

  // Import data (replaces existing data)
  public importData(data: { [key: string]: PaperOption[] }): boolean {
    try {
      let allSuccess = true;
      
      Object.entries(data).forEach(([category, papers]) => {
        if (['Inner Text', 'Cover', 'Endpapers'].includes(category)) {
          if (!this.savePapers(category as StorageCategory, papers)) {
            allSuccess = false;
          }
        }
      });
      
      return allSuccess;
    } catch (error) {
      console.error('Failed to import data:', error);
      return false;
    }
  }

  // Get last updated timestamp
  public getLastUpdated(): Date | null {
    if (!this.isClient) return null;
    
    try {
      const timestamp = localStorage.getItem(STORAGE_KEYS.LAST_UPDATED);
      return timestamp ? new Date(timestamp) : null;
    } catch (error) {
      console.warn('Failed to get last updated timestamp:', error);
      return null;
    }
  }

  // Clear all stored data
  public clearAllData(): boolean {
    if (!this.isClient) return false;
    
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
      return true;
    } catch (error) {
      console.error('Failed to clear all data:', error);
      return false;
    }
  }

  // Get storage usage info
  public getStorageInfo(): { [key: string]: number } | null {
    if (!this.isClient) return null;
    
    try {
      const info: { [key: string]: number } = {};
      
      Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
        const data = localStorage.getItem(key);
        info[name] = data ? new Blob([data]).size : 0;
      });
      
      return info;
    } catch (error) {
      console.warn('Failed to get storage info:', error);
      return null;
    }
  }
}

// Export singleton instance
export const paperStorage = PaperStorageService.getInstance();

// Export convenience functions
export const {
  loadPapers,
  savePapers,
  addPaper,
  updatePaper,
  deletePaper,
  getPaper,
  searchPapers,
  filterPapers,
  resetCategory,
  resetAllCategories,
  exportAllData,
  importData,
  getLastUpdated,
  clearAllData,
  getStorageInfo
} = paperStorage;