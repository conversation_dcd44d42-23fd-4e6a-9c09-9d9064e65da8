// Paper Options Card Configurations
// This file contains the configuration objects for different paper categories
// to be used with the PaperOptionsCard component

import { PaperOptionsConfig } from '@/components/paper-options-card';
import { paperDataUtils, innerTextPapers, coverPapers, endpaperPapers } from './paper-data';

// Inner Text Paper Configuration
export const innerTextConfig: PaperOptionsConfig = {
  title: 'Inner Text Paper Options',
  paperTypes: ['Pre-Cut', 'Roll'],
  finishes: [
    '546.1', '596.9', '444.5', '635.0', '787.4', '889.0', '965.2', // Sheet widths
    '-' // For rolls
  ],
  printingSides: ['Height', 'Width'],
  additionalFields: {
    costTon: {
      label: 'Cost/Ton ($)',
      type: 'number',
      defaultValue: undefined
    }
  },
  defaultValues: {
    id: '',
    name: '',
    paperType: 'Pre-Cut',
    weight: 80,
    color: '', // Sheet H (mm)
    finish: '', // Sheet W (mm)
    printingSide: 'Height',
    quantity: 100, // Caliper (µm)
    notes: '', // Cost/Ream ($)
    costTon: undefined
  },
  initialData: innerTextPapers
};

// Cover Paper Configuration
export const coverConfig: PaperOptionsConfig = {
  title: 'Cover Paper Options',
  paperTypes: ['Pre-Cut'],
  finishes: [
    '635', '700', '965', '1000' // Common cover sheet dimensions
  ],
  printingSides: ['Height', 'Width'],
  additionalFields: {
    costTon: {
      label: 'Cost/Ton ($)',
      type: 'number',
      defaultValue: undefined
    }
  },
  defaultValues: {
    id: '',
    name: '',
    paperType: 'Pre-Cut',
    weight: 250,
    color: '', // Sheet H (mm)
    finish: '', // Sheet W (mm)
    printingSide: 'Height',
    quantity: 280, // Caliper (µm)
    notes: '', // Cost/Ream ($)
    costTon: undefined
  },
  initialData: coverPapers
};

// Endpaper Configuration
export const endpaperConfig: PaperOptionsConfig = {
  title: 'Endpaper Options',
  paperTypes: ['Pre-Cut'],
  finishes: [
    '787.4', '650', '900', '1092.2' // Common endpaper sheet dimensions
  ],
  printingSides: ['Height', 'Width'],
  additionalFields: {
    costTon: {
      label: 'Cost/Ton ($)',
      type: 'number',
      defaultValue: undefined
    }
  },
  defaultValues: {
    id: '',
    name: '',
    paperType: 'Pre-Cut',
    weight: 120,
    color: '', // Sheet H (mm)
    finish: '', // Sheet W (mm)
    printingSide: 'Height',
    quantity: 140, // Caliper (µm)
    notes: '', // Cost/Ream ($)
    costTon: undefined
  },
  initialData: endpaperPapers
};

// Configuration factory function
export const createPaperConfig = (category: 'Inner Text' | 'Cover' | 'Endpapers'): PaperOptionsConfig => {
  switch (category) {
    case 'Inner Text':
      return innerTextConfig;
    case 'Cover':
      return coverConfig;
    case 'Endpapers':
      return endpaperConfig;
    default:
      throw new Error(`Unknown paper category: ${category}`);
  }
};

// Dynamic configuration generator based on existing data
export const generateConfigFromData = (category: string): PaperOptionsConfig => {
  const papers = paperDataUtils.getPapersByCategory(category);
  const paperTypes = [...new Set(papers.map(p => p.paperType))];
  const finishes = [...new Set(papers.map(p => p.finish))];
  const printingSides = [...new Set(papers.map(p => p.printingSide))];
  
  // Calculate average values for defaults
  const avgWeight = papers.length > 0 ? Math.round(papers.reduce((sum, p) => sum + p.weight, 0) / papers.length) : 80;
  const avgQuantity = papers.length > 0 ? Math.round(papers.reduce((sum, p) => sum + p.quantity, 0) / papers.length) : 100;

  return {
    title: `${category} Paper Options`,
    paperTypes,
    finishes,
    printingSides,
    additionalFields: {
      costTon: {
        label: 'Cost/Ton ($)',
        type: 'number',
        defaultValue: undefined
      }
    },
    defaultValues: {
      id: '',
      name: '',
      paperType: paperTypes[0] || 'Pre-Cut',
      weight: avgWeight,
      color: '',
      finish: '',
      printingSide: printingSides[0] || 'Height',
      quantity: avgQuantity,
      notes: '',
      costTon: undefined
    },
    initialData: papers
  };
};

// Export all configurations
export const paperConfigs = {
  innerText: innerTextConfig,
  cover: coverConfig,
  endpaper: endpaperConfig
};

// Helper function to get all available configurations
export const getAllConfigs = (): { [key: string]: PaperOptionsConfig } => {
  return paperConfigs;
};

// Helper function to get configuration by category name
export const getConfigByCategory = (category: string): PaperOptionsConfig | null => {
  const normalizedCategory = category.toLowerCase().replace(/\s+/g, '');
  
  switch (normalizedCategory) {
    case 'innertext':
      return innerTextConfig;
    case 'cover':
      return coverConfig;
    case 'endpapers':
    case 'endpaper':
      return endpaperConfig;
    default:
      return null;
  }
};