"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { LanguageSwitcher } from "@/components/language-switcher";
import { SpotlightTracker } from "@/components/spotlight-tracker";
import TabTracker from "@/components/tab-tracker";
import PaperOptionsCard, { PaperOptionsConfig } from '@/components/paper-options-card';
import JobSpecificationsCard from '@/components/job-specifications-card';
import ProductionParametersCard from '@/components/production-parameters-card';

// Language translations
const translations = {
  en: {
    title: "Paper Cost Estimator v23.0",
    language: "Language",
    theme: "Theme",
    tabInnerText: "Inner Text",
    tabCover: "Cover",
    tabEndpapers: "Endpapers",
    jobSpecs: "Job Specifications",
    productionParams: "Production Parameters",
    unitConverter: "Unit Converter",
    jobName: "Job Name",
    paperType: "Paper Type",
    paperWeight: "Paper Weight (gsm)",
    finishedSize: "Finished Size",
    quantity: "Quantity",
    colors: "Colors",
    sides: "Sides",
    calculate: "Calculate Cost"
  },
  "zh-cn": {
    title: "纸张成本估算器 v23.0",
    language: "语言",
    theme: "主题",
    tabInnerText: "内文",
    tabCover: "封面",
    tabEndpapers: "环衬",
    jobSpecs: "工作规格",
    productionParams: "生产参数",
    unitConverter: "单位转换器",
    jobName: "工作名称",
    paperType: "纸张类型",
    paperWeight: "纸张重量 (克)",
    finishedSize: "成品尺寸",
    quantity: "数量",
    colors: "颜色",
    sides: "面数",
    calculate: "计算成本"
  },
  "zh-tw": {
    title: "紙張成本估算器 v23.0",
    language: "語言",
    theme: "主題",
    tabInnerText: "內文",
    tabCover: "封面",
    tabEndpapers: "環襯",
    jobSpecs: "工作規格",
    productionParams: "生產參數",
    unitConverter: "單位轉換器",
    jobName: "工作名稱",
    paperType: "紙張類型",
    paperWeight: "紙張重量 (克)",
    finishedSize: "成品尺寸",
    quantity: "數量",
    colors: "顏色",
    sides: "面數",
    calculate: "計算成本"
  }
};

export default function PaperCostEstimator() {
  const [language, setLanguage] = useState<"en" | "zh-cn" | "zh-tw">("en");
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [activeTab, setActiveTab] = useState("innerText");
  
  const t = translations[language] || translations.en;

  // Paper options configurations
  const innerTextConfig: PaperOptionsConfig = {
    title: 'Inner Text Paper Options',
    paperTypes: ['Offset', 'Newsprint', 'Cardstock', 'Coated', 'Uncoated', 'Recycled'],
    finishes: ['Matte', 'Gloss', 'Satin', 'Smooth', 'Textured'],
    printingSides: ['Single Side', 'Both Sides'],
    defaultValues: {
      name: '',
      paperType: 'Offset',
      weight: 80,
      color: 'White',
      finish: 'Matte',
      printingSide: 'Both Sides',
      quantity: 1000,
      notes: ''
    },
    initialData: [
      {
        id: '1',
        name: 'Main Text Paper',
        paperType: 'Offset',
        weight: 80,
        color: 'White',
        finish: 'Matte',
        printingSide: 'Both Sides',
        quantity: 1000,
        notes: 'Standard text paper for book content'
      },
      {
        id: '2',
        name: 'Chapter Dividers',
        paperType: 'Cardstock',
        weight: 200,
        color: 'Cream',
        finish: 'Smooth',
        printingSide: 'Single Side',
        quantity: 50,
        notes: 'Heavier paper for chapter separations'
      }
    ]
  };

  const coverConfig: PaperOptionsConfig = {
    title: 'Cover Paper Options',
    paperTypes: ['Cardstock', 'Cover Stock', 'Art Paper', 'Kraft', 'Linen', 'Textured'],
    finishes: ['Gloss', 'Matte', 'Satin', 'Silk', 'Textured', 'Embossed'],
    printingSides: ['Single Side', 'Both Sides'],
    additionalFields: {
      coating: {
        label: 'Coating',
        type: 'select',
        options: ['UV Coating', 'Lamination', 'Varnish', 'Aqueous Coating', 'None'],
        defaultValue: 'UV Coating'
      }
    },
    defaultValues: {
      name: '',
      paperType: 'Cardstock',
      weight: 300,
      color: 'White',
      finish: 'Gloss',
      coating: 'UV Coating',
      printingSide: 'Single Side',
      quantity: 1000,
      notes: ''
    },
    initialData: [
      {
        id: '1',
        name: 'Front Cover',
        paperType: 'Cardstock',
        weight: 300,
        color: 'White',
        finish: 'Gloss',
        coating: 'UV Coating',
        printingSide: 'Single Side',
        quantity: 1000,
        notes: 'High-quality cover with UV protection'
      },
      {
        id: '2',
        name: 'Back Cover',
        paperType: 'Cardstock',
        weight: 300,
        color: 'White',
        finish: 'Matte',
        coating: 'Lamination',
        printingSide: 'Single Side',
        quantity: 1000,
        notes: 'Matching back cover with matte finish'
      }
    ]
  };

  const endpaperConfig: PaperOptionsConfig = {
    title: 'Endpaper Paper Options',
    paperTypes: ['Cotton', 'Textured', 'Linen', 'Kraft', 'Handmade'],
    finishes: ['Smooth', 'Textured', 'Ribbed', 'Laid', 'Wove'],
    printingSides: ['Single Side', 'Both Sides'],
    additionalFields: {
      pattern: {
        label: 'Pattern',
        type: 'select',
        options: ['Solid', 'Marbled', 'Decorative', 'Custom Print', 'None'],
        defaultValue: 'Solid'
      }
    },
    defaultValues: {
      name: '',
      paperType: 'Cotton',
      weight: 120,
      color: 'White',
      finish: 'Smooth',
      pattern: 'Solid',
      printingSide: 'Single Side',
      quantity: 1000,
      notes: ''
    },
    initialData: [
      {
        id: '1',
        name: 'Front Endpaper',
        paperType: 'Cotton',
        weight: 120,
        color: 'Cream',
        finish: 'Smooth',
        pattern: 'Solid',
        printingSide: 'Single Side',
        quantity: 1000,
        notes: 'High-quality endpaper for book binding'
      },
      {
        id: '2',
        name: 'Back Endpaper',
        paperType: 'Cotton',
        weight: 120,
        color: 'Cream',
        finish: 'Smooth',
        pattern: 'Solid',
        printingSide: 'Single Side',
        quantity: 1000,
        notes: 'Matching back endpaper'
      }
    ]
  };
  
  // Tab switching functionality
  useEffect(() => {
    const handleTabClick = (e: Event) => {
      const target = e.target as HTMLElement;
      const button = target.closest('.tab-item') as HTMLElement;
      if (!button) return;
      
      const tabName = button.dataset.tab;
      if (tabName && tabName !== activeTab) {
        setActiveTab(tabName);
        
        // Update active states
        document.querySelectorAll('.tab-item').forEach(tab => {
          tab.classList.remove('active');
        });
        button.classList.add('active');
        
        // Update content visibility
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.remove('active');
        });
        const targetContent = document.getElementById(`${tabName}-content`);
        if (targetContent) {
          targetContent.classList.add('active');
        }
      }
    };
    
    document.addEventListener('click', handleTabClick);
    return () => document.removeEventListener('click', handleTabClick);
  }, [activeTab]);
  
  const handleLanguageChange = (newLanguage: string) => {
    if (newLanguage === language) return;
    
    setIsTransitioning(true);
    
    // Add fade-out class to all text elements
    const textElements = document.querySelectorAll('.translatable');
    textElements.forEach(el => {
      el.classList.add('text-transition-out');
    });
    
    setTimeout(() => {
      setLanguage(newLanguage as "en" | "zh-cn" | "zh-tw");
      
      setTimeout(() => {
        textElements.forEach(el => {
          el.classList.remove('text-transition-out');
          el.classList.add('text-transition-in');
        });
        
        setTimeout(() => {
          textElements.forEach(el => {
            el.classList.remove('text-transition-in');
          });
          setIsTransitioning(false);
        }, 150);
      }, 50);
    }, 150);
    
    // Dispatch custom event for tab indicator update
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('languageChanged', { 
        detail: { language: newLanguage } 
      }));
    }, 50);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <SpotlightTracker />
      <TabTracker />
      {/* Header */}
      <header className="sticky top-0 left-0 right-0 z-[1000] bg-white dark:bg-gray-900 shadow-md w-full py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between">
          {/* Brand and Tabs Group (Left) */}
          <div className="flex items-center space-x-6">
            <h1 className="text-xl font-semibold tracking-tight brand-title">
              Prosperous Codex
            </h1>
            <div className="tabs-container">
              <div className="tabs-wrapper">
                <button className="tab-item active" data-tab="innerText">
                  <span className="translatable">{translations[language].tabInnerText || 'Inner Text'}</span>
                </button>
                <button className="tab-item" data-tab="cover">
                  <span className="translatable">{translations[language].tabCover || 'Cover'}</span>
                </button>
                <button className="tab-item" data-tab="endpapers">
                  <span className="translatable">{translations[language].tabEndpapers || 'Endpapers'}</span>
                </button>
                <div className="tab-indicator"></div>
              </div>
            </div>
          </div>
          {/* Controls Group (Right) */}
           <div className="flex items-center" id="new-header-controls">
             <LanguageSwitcher 
               currentLanguage={language}
               onLanguageChange={handleLanguageChange}
             />
             <div className="ml-4">
               <ThemeSwitcher />
             </div>
           </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Top Three Cards - Job Specifications, Production Parameters, Unit Converter */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Job Specifications Card */}
          <JobSpecificationsCard />

          {/* Production Parameters Card */}
          <ProductionParametersCard />

          {/* Unit Converter Card */}
          <Card className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-shadow duration-300" data-glow>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center translatable">
                <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                {t.unitConverter}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="weight-gsm" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Paper Weight (GSM)
                </Label>
                <Input
                  id="weight-gsm"
                  type="number"
                  placeholder="80"
                  className="mt-1 h-9 text-sm"
                  data-glow
                />
              </div>
              <div>
                <Label htmlFor="weight-lbs" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Weight (lbs)
                </Label>
                <Input
                  id="weight-lbs"
                  type="number"
                  placeholder="21.3"
                  className="mt-1 h-9 text-sm"
                  data-glow
                  readOnly
                />
              </div>
              <div>
                <Label htmlFor="thickness" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Thickness (mm)
                </Label>
                <Input
                  id="thickness"
                  type="number"
                  placeholder="0.1"
                  className="mt-1 h-9 text-sm"
                  data-glow
                />
              </div>
              <Button className="w-full h-9 text-sm translatable" variant="outline" data-glow>
                Convert Units
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Tab Content Sections */}
        <div className="tab-content active" id="innerText-content">
          <div className="grid grid-cols-1 gap-6">
            <PaperOptionsCard config={innerTextConfig} />
          </div>
        </div>

        <div className="tab-content" id="cover-content">
          <div className="grid grid-cols-1 gap-6">
            <PaperOptionsCard config={coverConfig} />
          </div>
        </div>

        <div className="tab-content" id="endpapers-content">
          {/* Endpaper Paper Options Card */}
          <PaperOptionsCard config={endpaperConfig} />
        </div>
      </main>
    </div>
  );
}
