@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Theme and Language Switcher Styles */
/* Theme Tab Switcher Styles - 3 Mode - Concave Design */
.theme-switch {
  position: relative;
  display: flex;
  background: transparent;
  border-radius: 9999px;
  padding: 4px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  align-items: center;
  gap: 2px;
  width: fit-content;
  height: 36px;
  justify-content: center;
}

.theme-icon {
  position: relative;
  color: #6b7280;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 6px 10px;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  height: 28px;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(229, 231, 235, 0.4);
  cursor: pointer;
  outline: none;
  backdrop-filter: blur(8px);
}

.theme-icon:hover:not(.active) {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(229, 231, 235, 0.6);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .theme-icon {
  color: #9ca3af;
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.4);
}

.dark .theme-icon:hover:not(.active) {
  color: #d1d5db;
  background: rgba(31, 41, 55, 0.9);
  border-color: rgba(75, 85, 99, 0.6);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Active states with concave effect */
.theme-switch.light .sun-icon,
.theme-switch.system .system-icon,
.theme-switch.dark .moon-icon {
  color: #5E6AD2;
  font-weight: 600;
  background: rgba(94, 106, 210, 0.1);
  border: 1px solid rgba(94, 106, 210, 0.3);
  box-shadow: inset 0 2px 4px rgba(94, 106, 210, 0.2), inset 0 1px 2px rgba(0, 0, 0, 0.1);
  transform: translateY(1px);
}

.dark .theme-switch.light .sun-icon,
.dark .theme-switch.system .system-icon,
.dark .theme-switch.dark .moon-icon {
  color: #8b93f1;
  background: rgba(139, 147, 241, 0.15);
  border: 1px solid rgba(139, 147, 241, 0.3);
  box-shadow: inset 0 2px 4px rgba(139, 147, 241, 0.2), inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Theme switch toggle styles removed - using concave design instead */

/* Enhanced Language Switcher Styles - Concave Design */
.language-switcher {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.language-trigger {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  padding: 0.625rem 1rem;
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted) / 0.3) 100%);
  border: 1.5px solid hsl(var(--border));
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.875rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  min-width: 4rem;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
}

.language-pill-switcher {
  /* No styling - buttons are independent elements */
}

.language-trigger::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.language-trigger:hover {
  background: linear-gradient(135deg, hsl(var(--muted) / 0.8) 0%, hsl(var(--muted) / 0.4) 100%);
  border-color: hsl(var(--primary) / 0.4);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.language-trigger:hover::before {
  opacity: 1;
}

.language-trigger:focus {
  outline: none;
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2), 0 4px 12px rgba(0, 0, 0, 0.15);
}

.language-current {
  font-weight: 700;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.language-chevron {
  width: 1.125rem;
  height: 1.125rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: hsl(var(--primary) / 0.7);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.language-chevron.open {
  transform: rotate(180deg) scale(1.1);
  color: hsl(var(--primary));
}

.language-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  min-width: 12rem;
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted) / 0.1) 100%);
  border: 1.5px solid hsl(var(--border));
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 50;
  overflow: hidden;
  animation: slideDown 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(12px);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-0.75rem) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 1rem 1.25rem;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  position: relative;
  overflow: hidden;
}

.language-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--primary) / 0.05) 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.language-option:hover {
  background: linear-gradient(135deg, hsl(var(--muted) / 0.8) 0%, hsl(var(--muted) / 0.4) 100%);
  transform: translateX(4px);
}

.language-option:hover::before {
  opacity: 1;
}

.language-option.active {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.15) 0%, hsl(var(--primary) / 0.08) 100%);
  color: hsl(var(--primary));
  border-left: 3px solid hsl(var(--primary));
}

.language-option.active .language-short {
  color: hsl(var(--primary));
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.language-short {
  font-weight: 600;
  font-size: 0.875rem;
  min-width: 2rem;
  color: hsl(var(--muted-foreground));
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: linear-gradient(135deg, hsl(var(--foreground)) 0%, hsl(var(--muted-foreground)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.2s ease;
}

.language-full {
  font-size: 0.9rem;
  color: hsl(var(--foreground));
  font-weight: 500;
  transition: all 0.2s ease;
}

.language-option.active .language-full {
  color: hsl(var(--primary));
  font-weight: 600;
}

.language-option:first-child {
  border-top-left-radius: 0.875rem;
  border-top-right-radius: 0.875rem;
}

.language-option:last-child {
  border-bottom-left-radius: 0.875rem;
  border-bottom-right-radius: 0.875rem;
}



/* Language Tab Switcher Styles */
.language-pill-switcher {
  position: relative;
  display: flex;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 9999px; /* Changed to fully rounded */
  padding: 2px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  align-items: center;
  gap: 0;
  width: fit-content; /* Keep this as fit-content */
  height: 36px;
  justify-content: center;
  padding: 2px; /* Adjusted padding */
}

.dark .language-pill-switcher {
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.6);
}

.language-pill-option {
  position: relative;
  color: #6b7280;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 6px 10px;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 28px;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(229, 231, 235, 0.4);
  cursor: pointer;
  outline: none;
  backdrop-filter: blur(8px);
}

.language-pill-option:hover:not(.active) {
  color: #374151;
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(229, 231, 235, 0.6);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.language-pill-option.active {
  color: #5E6AD2;
  font-weight: 600;
  background: rgba(94, 106, 210, 0.1);
  border: 1px solid rgba(94, 106, 210, 0.3);
  box-shadow: inset 0 2px 4px rgba(94, 106, 210, 0.2), inset 0 1px 2px rgba(0, 0, 0, 0.1);
  transform: translateY(1px);
}

.dark .language-pill-option {
  color: #9ca3af;
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.4);
}

.dark .language-pill-option:hover:not(.active) {
  color: #d1d5db;
  background: rgba(31, 41, 55, 0.9);
  border-color: rgba(75, 85, 99, 0.6);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark .language-pill-option.active {
  color: #8b93f1;
  background: rgba(139, 147, 241, 0.15);
  border: 1px solid rgba(139, 147, 241, 0.3);
  box-shadow: inset 0 2px 4px rgba(139, 147, 241, 0.2), inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Language pill indicator styles removed - using concave design instead */

/* Text transition animations */
.text-transition-out {
  animation: textFadeOut 0.15s ease-out forwards;
}

.text-transition-in {
  animation: textFadeIn 0.15s ease-in forwards;
}

@keyframes textFadeOut {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-5px);
  }
}

@keyframes textFadeIn {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Advanced Spotlight Glow Effect System */
[data-glow] {
  --border-size: 1px;
  --spotlight-size: 600px;
  --hue: 210;
  --saturation: 100;
  --lightness: 70;
  --bg-spot-opacity: 0.1;
  --border-spot-opacity: 0.35;
  --border-light-opacity: 0.4;
  --x: 50%;
  --y: 50%;
  
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid hsl(var(--border));
  position: relative;
  touch-action: none;
  /* GPU acceleration for better performance */
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Light mode - simple hover effect */
:not(.dark) [data-glow]:hover {
  border-color: hsl(var(--primary) / 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

/* Dark mode - advanced spotlight effect */
.dark [data-glow] {
  background-image: radial-gradient(
    var(--spotlight-size) var(--spotlight-size) at
    var(--x) var(--y),
    hsl(var(--hue) calc(var(--saturation) * 1%) calc(var(--lightness) * 1%) / var(--bg-spot-opacity)),
    transparent
  );
  border: var(--border-size) solid hsl(var(--border));
}

.dark [data-glow]::before,
.dark [data-glow]::after {
  pointer-events: none;
  content: "";
  position: absolute;
  inset: calc(var(--border-size) * -1);
  border: var(--border-size) solid transparent;
  border-radius: inherit;
  background-repeat: no-repeat;
  mask:
    linear-gradient(transparent, transparent),
    linear-gradient(white, white);
  mask-clip: padding-box, border-box;
  mask-composite: intersect;
  transition: opacity 0.3s ease;
  /* GPU acceleration for pseudo-elements */
  will-change: opacity, transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.dark [data-glow]::before {
  background-image: radial-gradient(
    calc(var(--spotlight-size) * 0.75) calc(var(--spotlight-size) * 0.75) at
    var(--x) var(--y),
    hsl(var(--hue) calc(var(--saturation) * 1%) calc(var(--lightness) * 0.7%) / var(--border-spot-opacity)),
    transparent 100%
  );
  filter: brightness(1.5);
}

.dark [data-glow]::after {
  background-image: radial-gradient(
    calc(var(--spotlight-size) * 0.4) calc(var(--spotlight-size) * 0.4) at
    var(--x) var(--y),
    hsl(0 100% 100% / var(--border-light-opacity)),
    transparent 100%
  );
}

/* Enhanced hover effects for dark mode */
.dark [data-glow]:hover {
  transform: translateY(-4px) translateZ(0);
  box-shadow: 
    0 20px 35px rgba(0, 0, 0, 0.3),
    0 8px 40px 0 rgba(85, 110, 255, 0.25), 
    0 0 0 1px rgba(94, 106, 210, 0.6);
  border-color: rgba(94, 106, 210, 0.7);
  z-index: 10;
  will-change: transform, box-shadow;
}

.dark [data-glow]:hover::before {
  opacity: 1.6;
}

.dark [data-glow]:hover::after {
  opacity: 1.2;
}

/* Content enhancement on hover in dark mode */
.dark [data-glow]:hover h1,
.dark [data-glow]:hover h2,
.dark [data-glow]:hover h3,
.dark [data-glow]:hover .card-title {
  color: hsl(var(--foreground));
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.dark [data-glow]:hover .card-description,
.dark [data-glow]:hover label {
  color: rgba(255, 255, 255, 0.8);
}

/* Color variations for different card types */
[data-glow]:nth-child(1) {
  --hue: 210; /* Blue */
}

[data-glow]:nth-child(2) {
  --hue: 142; /* Green */
}

[data-glow]:nth-child(3) {
  --hue: 262; /* Purple */
}

[data-glow]:nth-child(4) {
  --hue: 35; /* Orange */
}

/* Pulse glow animation for special states */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
  }
  50% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.5), 0 0 30px hsl(var(--primary) / 0.3);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes fade-out-pulse {
  0% {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
  }
  100% {
    box-shadow: 0 0 0 hsl(var(--primary) / 0);
  }
}

.fade-out-glow {
  animation: fade-out-pulse 1.5s forwards ease-out;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* Input and Select styling with enhanced contrast */
input, select, textarea {
  @apply bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600;
  background-color: rgb(255 255 255 / 0.95) !important;
}

.dark input, .dark select, .dark textarea {
  background-color: rgb(55 65 81 / 0.95) !important;
  border-color: rgb(75 85 99 / 0.8) !important;
}

/* Enhanced input field contrast */
[data-glow] input,
[data-glow] select,
[data-glow] textarea,
[data-glow] [role="combobox"] {
  background-color: rgb(248 250 252 / 0.98) !important;
  border: 1px solid rgb(203 213 225 / 0.8) !important;
  box-shadow: 
    0 1px 2px 0 rgb(0 0 0 / 0.05),
    inset 0 1px 0 0 rgb(255 255 255 / 0.1);
  backdrop-filter: blur(8px);
}

.dark [data-glow] input,
.dark [data-glow] select,
.dark [data-glow] textarea,
.dark [data-glow] [role="combobox"] {
  background-color: rgb(30 41 59 / 0.98) !important;
  border: 1px solid rgb(71 85 105 / 0.8) !important;
  box-shadow: 
    0 1px 2px 0 rgb(0 0 0 / 0.2),
    inset 0 1px 0 0 rgb(255 255 255 / 0.05);
}

/* Focus states with better contrast */
[data-glow] input:focus,
[data-glow] select:focus,
[data-glow] textarea:focus,
[data-glow] [role="combobox"]:focus {
  background-color: rgb(255 255 255 / 1) !important;
  border-color: rgb(59 130 246 / 0.8) !important;
  box-shadow: 
    0 0 0 3px rgb(59 130 246 / 0.1),
    0 1px 2px 0 rgb(0 0 0 / 0.05),
    inset 0 1px 0 0 rgb(255 255 255 / 0.2);
}

.dark [data-glow] input:focus,
.dark [data-glow] select:focus,
.dark [data-glow] textarea:focus,
.dark [data-glow] [role="combobox"]:focus {
  background-color: rgb(15 23 42 / 1) !important;
  border-color: rgb(59 130 246 / 0.8) !important;
  box-shadow: 
    0 0 0 3px rgb(59 130 246 / 0.2),
    0 1px 2px 0 rgb(0 0 0 / 0.3),
    inset 0 1px 0 0 rgb(255 255 255 / 0.1);
}

/* Enhanced input field contrast */
input[type="text"],
input[type="number"],
select,
textarea {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
}

.dark input[type="text"],
.dark input[type="number"],
.dark select,
.dark textarea {
  background-color: rgba(31, 41, 55, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.3),
    inset 0 1px 2px rgba(255, 255, 255, 0.05);
}

/* Enhanced styles for glow elements */
[data-glow] input[type="text"],
[data-glow] input[type="number"],
[data-glow] select,
[data-glow] textarea {
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

.dark [data-glow] input[type="text"],
.dark [data-glow] input[type="number"],
.dark [data-glow] select,
.dark [data-glow] textarea {
  background-color: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.4),
    inset 0 1px 2px rgba(255, 255, 255, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Focus states */
input[type="text"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 2px rgba(0, 0, 0, 0.05),
    0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark input[type="text"]:focus,
.dark input[type="number"]:focus,
.dark select:focus,
.dark textarea:focus {
  border-color: rgba(96, 165, 250, 0.5);
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 2px rgba(255, 255, 255, 0.05),
    0 0 0 3px rgba(96, 165, 250, 0.2);
}

/* Header Tabs and Brand Styles */
.brand-title {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.dark .brand-title {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.tabs-container {
  position: relative;
  display: flex;
  align-items: center;
}

.tabs-wrapper {
  position: relative;
  display: flex;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 2px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .tabs-wrapper {
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.6);
}

.tab-item {
  position: relative;
  z-index: 2;
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  outline: none;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.tab-item:hover:not(.active) {
  color: #374151;
  background: rgba(156, 163, 175, 0.1);
}

.tab-item.active {
  color: #5E6AD2;
  font-weight: 600;
  transition: color 0.1s ease, font-weight 0.1s ease;
}

.dark .tab-item {
  color: #9ca3af;
}

.dark .tab-item:hover:not(.active) {
  color: #d1d5db;
  background: rgba(156, 163, 175, 0.1);
}

.dark .tab-item.active {
  color: #8b93f1;
  transition: color 0.1s ease;
}

.tab-indicator {
  position: absolute;
  top: 3px;
  left: 3px;
  height: calc(100% - 6px);
  background: white;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.dark .tab-indicator {
  background: #374151;
  border: 1px solid rgba(75, 85, 99, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Tab content animations */
.tab-content {
  display: none;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease-out;
}

.tab-content.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Editable field styles for double-tap functionality */
.editable-field {
  transition: all 0.2s ease;
  border-radius: 4px;
}

.editable-field:hover {
  background-color: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
}

.dark .editable-field:hover {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

/* Enhanced table-like grid styling */
.endpaper-grid {
  border-collapse: separate;
  border-spacing: 0;
}

.endpaper-grid .grid {
  border-radius: 8px;
  overflow: hidden;
}

.inner-text-grid {
  border-collapse: separate;
  border-spacing: 0;
}

.inner-text-grid .grid {
  border-radius: 8px;
  overflow: hidden;
}

.cover-grid {
  border-collapse: separate;
  border-spacing: 0;
}

.cover-grid .grid {
  border-radius: 8px;
  overflow: hidden;
}

.endpaper-grid-row {
  transition: background-color 0.2s ease;
}

.endpaper-grid-row:hover {
  background-color: rgba(59, 130, 246, 0.02);
}

.dark .endpaper-grid-row:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Language Switcher Pill Design */
/* Legacy language switcher styles completely removed */



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
