"use client";

import { useState, useRef, useEffect } from "react";

interface LanguageSwitcherProps {
  currentLanguage: string;
  onLanguageChange: (language: string) => void;
}

export function LanguageSwitcher({ currentLanguage, onLanguageChange }: LanguageSwitcherProps) {
  const languages = [
    { code: "en", short: "EN", full: "English" },
    { code: "zh-cn", short: "简", full: "简体中文" },
    { code: "zh-tw", short: "繁", full: "繁體中文" }
  ];

  return (
    <>
      {languages.map((language) => (
        <button
          key={language.code}
          className={`language-pill-option ${
            language.code === currentLanguage ? 'active' : ''
          }`}
          onClick={() => onLanguageChange(language.code)}
          aria-label={`Switch to ${language.full}`}
        >
          {language.short}
        </button>
      ))}
    </>
  );
}