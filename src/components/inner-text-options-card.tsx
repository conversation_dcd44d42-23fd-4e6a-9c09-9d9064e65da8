'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Trash2, Edit, Plus, Save, X } from 'lucide-react';

interface InnerTextOption {
  id: string;
  name: string;
  paperType: string;
  weight: number;
  color: string;
  finish: string;
  printingSide: string;
  quantity: number;
  notes: string;
}

const InnerTextOptionsCard: React.FC = () => {
  const [innerTextOptions, setInnerTextOptions] = useState<InnerTextOption[]>([
    {
      id: '1',
      name: 'Main Text Paper',
      paperType: 'Offset',
      weight: 80,
      color: 'White',
      finish: '<PERSON><PERSON>',
      printingSide: 'Both Sides',
      quantity: 1000,
      notes: 'Standard text paper for book content'
    },
    {
      id: '2',
      name: 'Chapter Dividers',
      paperType: 'Cardstock',
      weight: 200,
      color: 'Cream',
      finish: 'Smooth',
      printingSide: 'Single Side',
      quantity: 50,
      notes: 'Heavier paper for chapter separations'
    }
  ]);

  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState<string>('');
  const [isAdding, setIsAdding] = useState(false);
  const [newOption, setNewOption] = useState<Partial<InnerTextOption>>({
    name: '',
    paperType: 'Offset',
    weight: 80,
    color: 'White',
    finish: 'Matte',
    printingSide: 'Both Sides',
    quantity: 1000,
    notes: ''
  });

  const paperTypes = ['Offset', 'Newsprint', 'Cardstock', 'Coated', 'Uncoated', 'Recycled'];
  const finishes = ['Matte', 'Gloss', 'Satin', 'Smooth', 'Textured'];
  const printingSides = ['Single Side', 'Both Sides'];

  const handleDoubleClick = (id: string, field: string, value: string | number) => {
    setEditingId(id);
    setEditingField(field);
    setTempValue(value.toString());
  };

  const handleSave = () => {
    if (editingId && editingField) {
      setInnerTextOptions(prev => prev.map(option => {
        if (option.id === editingId) {
          const updatedOption = { ...option };
          if (editingField === 'weight' || editingField === 'quantity') {
            (updatedOption as any)[editingField] = parseInt(tempValue) || 0;
          } else {
            (updatedOption as any)[editingField] = tempValue;
          }
          return updatedOption;
        }
        return option;
      }));
    }
    setEditingId(null);
    setEditingField(null);
    setTempValue('');
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditingField(null);
    setTempValue('');
  };

  const handleDelete = (id: string) => {
    setInnerTextOptions(prev => prev.filter(option => option.id !== id));
  };

  const handleAddNew = () => {
    if (newOption.name) {
      const id = Date.now().toString();
      setInnerTextOptions(prev => [...prev, { ...newOption, id } as InnerTextOption]);
      setNewOption({
        name: '',
        paperType: 'Offset',
        weight: 80,
        color: 'White',
        finish: 'Matte',
        printingSide: 'Both Sides',
        quantity: 1000,
        notes: ''
      });
      setIsAdding(false);
    }
  };

  const renderEditableField = (option: InnerTextOption, field: keyof InnerTextOption, label: string) => {
    const isEditing = editingId === option.id && editingField === field;
    const value = option[field];

    if (isEditing) {
      if (field === 'paperType') {
        return (
          <div className="flex items-center gap-2">
            <Select value={tempValue} onValueChange={setTempValue}>
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {paperTypes.map(type => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button size="sm" onClick={handleSave}><Save className="h-3 w-3" /></Button>
            <Button size="sm" variant="outline" onClick={handleCancel}><X className="h-3 w-3" /></Button>
          </div>
        );
      }
      
      if (field === 'finish') {
        return (
          <div className="flex items-center gap-2">
            <Select value={tempValue} onValueChange={setTempValue}>
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {finishes.map(finish => (
                  <SelectItem key={finish} value={finish}>{finish}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button size="sm" onClick={handleSave}><Save className="h-3 w-3" /></Button>
            <Button size="sm" variant="outline" onClick={handleCancel}><X className="h-3 w-3" /></Button>
          </div>
        );
      }
      
      if (field === 'printingSide') {
        return (
          <div className="flex items-center gap-2">
            <Select value={tempValue} onValueChange={setTempValue}>
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {printingSides.map(side => (
                  <SelectItem key={side} value={side}>{side}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button size="sm" onClick={handleSave}><Save className="h-3 w-3" /></Button>
            <Button size="sm" variant="outline" onClick={handleCancel}><X className="h-3 w-3" /></Button>
          </div>
        );
      }
      
      if (field === 'notes') {
        return (
          <div className="flex items-center gap-2">
            <Textarea
              value={tempValue}
              onChange={(e) => setTempValue(e.target.value)}
              className="h-20 resize-none"
            />
            <div className="flex flex-col gap-1">
              <Button size="sm" onClick={handleSave}><Save className="h-3 w-3" /></Button>
              <Button size="sm" variant="outline" onClick={handleCancel}><X className="h-3 w-3" /></Button>
            </div>
          </div>
        );
      }
      
      return (
        <div className="flex items-center gap-2">
          <Input
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            className="h-8"
            type={field === 'weight' || field === 'quantity' ? 'number' : 'text'}
          />
          <Button size="sm" onClick={handleSave}><Save className="h-3 w-3" /></Button>
          <Button size="sm" variant="outline" onClick={handleCancel}><X className="h-3 w-3" /></Button>
        </div>
      );
    }

    return (
      <div
        className="editable-field cursor-pointer p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
        onDoubleClick={() => handleDoubleClick(option.id, field, value)}
        title="Double-click to edit"
      >
        {field === 'notes' ? (
          <div className="max-h-20 overflow-y-auto text-sm">
            {value || 'No notes'}
          </div>
        ) : (
          value
        )}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader className="pt-1 pb-1">
        <CardTitle className="flex items-center justify-between">
          Inner Text Paper Options
          <Button
            onClick={() => setIsAdding(true)}
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add New
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="inner-text-grid">
          <div className="grid grid-cols-9 gap-4 font-medium border-b pb-2 mb-4">
            <div>Name</div>
            <div>Paper Type</div>
            <div>Weight (GSM)</div>
            <div>Color</div>
            <div>Finish</div>
            <div>Printing Side</div>
            <div>Quantity</div>
            <div>Notes</div>
            <div>Actions</div>
          </div>
          
          {innerTextOptions.map((option) => (
            <div key={option.id} className="grid grid-cols-9 gap-4 py-2 border-b border-gray-100 dark:border-gray-800">
              <div>{renderEditableField(option, 'name', 'Name')}</div>
              <div>{renderEditableField(option, 'paperType', 'Paper Type')}</div>
              <div>{renderEditableField(option, 'weight', 'Weight')}</div>
              <div>{renderEditableField(option, 'color', 'Color')}</div>
              <div>{renderEditableField(option, 'finish', 'Finish')}</div>
              <div>{renderEditableField(option, 'printingSide', 'Printing Side')}</div>
              <div>{renderEditableField(option, 'quantity', 'Quantity')}</div>
              <div>{renderEditableField(option, 'notes', 'Notes')}</div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDoubleClick(option.id, 'name', option.name)}
                >
                  <Edit className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleDelete(option.id)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
          
          {isAdding && (
            <div className="grid grid-cols-9 gap-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
              <Input
                placeholder="Name"
                value={newOption.name || ''}
                onChange={(e) => setNewOption(prev => ({ ...prev, name: e.target.value }))}
                className="h-8"
              />
              <Select
                value={newOption.paperType}
                onValueChange={(value) => setNewOption(prev => ({ ...prev, paperType: value }))}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {paperTypes.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                type="number"
                placeholder="Weight"
                value={newOption.weight || ''}
                onChange={(e) => setNewOption(prev => ({ ...prev, weight: parseInt(e.target.value) || 0 }))}
                className="h-8"
              />
              <Input
                placeholder="Color"
                value={newOption.color || ''}
                onChange={(e) => setNewOption(prev => ({ ...prev, color: e.target.value }))}
                className="h-8"
              />
              <Select
                value={newOption.finish}
                onValueChange={(value) => setNewOption(prev => ({ ...prev, finish: value }))}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {finishes.map(finish => (
                    <SelectItem key={finish} value={finish}>{finish}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={newOption.printingSide}
                onValueChange={(value) => setNewOption(prev => ({ ...prev, printingSide: value }))}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {printingSides.map(side => (
                    <SelectItem key={side} value={side}>{side}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                type="number"
                placeholder="Quantity"
                value={newOption.quantity || ''}
                onChange={(e) => setNewOption(prev => ({ ...prev, quantity: parseInt(e.target.value) || 0 }))}
                className="h-8"
              />
              <Textarea
                placeholder="Notes"
                value={newOption.notes || ''}
                onChange={(e) => setNewOption(prev => ({ ...prev, notes: e.target.value }))}
                className="h-8 resize-none"
              />
              <div className="flex gap-2">
                <Button size="sm" onClick={handleAddNew}>
                  <Save className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline" onClick={() => setIsAdding(false)}>
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default InnerTextOptionsCard;