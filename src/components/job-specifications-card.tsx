"use client";

import { useState } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface JobSpecificationsProps {
  className?: string;
}

export default function JobSpecificationsCard({ className = "" }: JobSpecificationsProps) {
  const [pageHeightMm, setPageHeightMm] = useState("");
  const [pageHeightIn, setPageHeightIn] = useState("");
  const [pageWidthMm, setPageWidthMm] = useState("150");
  const [pageWidthIn, setPageWidthIn] = useState("");
  const [totalPages, setTotalPages] = useState("320");
  const [quantity, setQuantity] = useState("1000");
  const [bindingMethod, setBindingMethod] = useState("Saddle Stitch");
  const [spoilage, setSpoilage] = useState("5");

  // Convert mm to inches
  const mmToInches = (mm: string) => {
    const mmValue = parseFloat(mm);
    if (isNaN(mmValue)) return "";
    return (mmValue / 25.4).toFixed(2);
  };

  // Convert inches to mm
  const inchesToMm = (inches: string) => {
    const inchValue = parseFloat(inches);
    if (isNaN(inchValue)) return "";
    return (inchValue * 25.4).toFixed(0);
  };

  const handlePageHeightMmChange = (value: string) => {
    setPageHeightMm(value);
    setPageHeightIn(mmToInches(value));
  };

  const handlePageHeightInChange = (value: string) => {
    setPageHeightIn(value);
    setPageHeightMm(inchesToMm(value));
  };

  const handlePageWidthMmChange = (value: string) => {
    setPageWidthMm(value);
    setPageWidthIn(mmToInches(value));
  };

  const handlePageWidthInChange = (value: string) => {
    setPageWidthIn(value);
    setPageWidthMm(inchesToMm(value));
  };

  return (
    <Card className={`bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-shadow duration-300 ${className}`} data-glow>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
          Job Specifications
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Page Height Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="page-height-mm" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Page Height
              </Label>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                mm
              </span>
            </div>
            <Input
              id="page-height-mm"
              type="number"
              placeholder="e.g., 225"
              value={pageHeightMm}
              onChange={(e) => handlePageHeightMmChange(e.target.value)}
              className="h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              data-glow
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="page-height-in" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Page Height
              </Label>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                in
              </span>
            </div>
            <Input
              id="page-height-in"
              type="number"
              step="0.01"
              placeholder="e.g., 8.86"
              value={pageHeightIn}
              onChange={(e) => handlePageHeightInChange(e.target.value)}
              className="h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              data-glow
            />
          </div>
        </div>

        {/* Page Width Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="page-width-mm" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Page Width
              </Label>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                mm
              </span>
            </div>
            <Input
              id="page-width-mm"
              type="number"
              placeholder="150"
              value={pageWidthMm}
              onChange={(e) => handlePageWidthMmChange(e.target.value)}
              className="h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              data-glow
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="page-width-in" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Page Width
              </Label>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                in
              </span>
            </div>
            <Input
              id="page-width-in"
              type="number"
              step="0.01"
              placeholder="e.g., 5.91"
              value={pageWidthIn}
              onChange={(e) => handlePageWidthInChange(e.target.value)}
              className="h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              data-glow
            />
          </div>
        </div>

        {/* Total Pages and Quantity Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="total-pages" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Total Pages
            </Label>
            <Input
              id="total-pages"
              type="number"
              placeholder="320"
              value={totalPages}
              onChange={(e) => setTotalPages(e.target.value)}
              className="mt-1 h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              data-glow
            />
          </div>
          <div>
            <Label htmlFor="quantity" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Quantity
            </Label>
            <Input
              id="quantity"
              type="number"
              placeholder="1000"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              className="mt-1 h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              data-glow
            />
          </div>
        </div>

        {/* Binding Method and Spoilage Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="binding-method" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Binding Method
            </Label>
            <Select value={bindingMethod} onValueChange={setBindingMethod}>
              <SelectTrigger className="mt-1 h-9 text-sm" data-glow>
                <SelectValue placeholder="Select binding" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Saddle Stitch">Saddle Stitch</SelectItem>
                <SelectItem value="Perfect Bound">Perfect Bound</SelectItem>
                <SelectItem value="Spiral">Spiral</SelectItem>
                <SelectItem value="Wire-O">Wire-O</SelectItem>
                <SelectItem value="Case Bound">Case Bound</SelectItem>
                <SelectItem value="Coil Bound">Coil Bound</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="spoilage" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Spoilage (%)
            </Label>
            <Input
              id="spoilage"
              type="number"
              placeholder="5"
              value={spoilage}
              onChange={(e) => setSpoilage(e.target.value)}
              className="mt-1 h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              data-glow
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}