"use client";

import { useEffect } from "react";

export function SpotlightTracker() {
  useEffect(() => {
    let rafId: number | null = null;
    let lastX = 0;
    let lastY = 0;

    const syncPointer = (event: PointerEvent) => {
      const { clientX: x, clientY: y } = event;
      
      // Skip if position hasn't changed significantly (reduce unnecessary updates)
      if (Math.abs(x - lastX) < 2 && Math.abs(y - lastY) < 2) return;
      
      lastX = x;
      lastY = y;
      
      // Cancel previous frame if still pending
      if (rafId) cancelAnimationFrame(rafId);
      
      // Use requestAnimationFrame for smooth 60fps updates
      rafId = requestAnimationFrame(() => {
        // Update each glow card individually with relative positioning
        const glowCards = document.querySelectorAll('[data-glow]');
        
        glowCards.forEach(card => {
          const rect = card.getBoundingClientRect();
          const relativeX = x - rect.left;
          const relativeY = y - rect.top;
          
          // Set relative position for this specific card
          (card as HTMLElement).style.setProperty('--x', `${relativeX}px`);
          (card as HTMLElement).style.setProperty('--y', `${relativeY}px`);
        });
        
        rafId = null;
      });
    };
    
    // Use passive listener for better scroll performance
    document.body.addEventListener('pointermove', syncPointer, { passive: true });
    
    return () => {
      document.body.removeEventListener('pointermove', syncPointer);
      if (rafId) cancelAnimationFrame(rafId);
    };
  }, []);

  return null; // This component doesn't render anything
}