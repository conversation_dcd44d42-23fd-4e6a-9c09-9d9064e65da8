'use client';

import { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Trash2, Edit3, Plus, Check, X } from "lucide-react";

interface EndpaperOption {
  id: string;
  name: string;
  paperType: string;
  weight: number;
  color: string;
  pattern: string;
  printingSide: string;
  finish: string;
  quantity: number;
  notes: string;
}

interface EndpaperOptionsCardProps {
  translations: any;
  language: string;
}

const EndpaperOptionsCard: React.FC<EndpaperOptionsCardProps> = ({ translations, language }) => {
  const [endpaperOptions, setEndpaperOptions] = useState<EndpaperOption[]>([
    {
      id: '1',
      name: 'Front Endpaper',
      paperType: 'Cotton Paper',
      weight: 120,
      color: 'Cream',
      pattern: 'Marbled',
      printingSide: 'Single',
      finish: 'Matte',
      quantity: 100,
      notes: 'High-quality decorative endpaper'
    },
    {
      id: '2',
      name: 'Back Endpaper',
      paperType: 'Textured Paper',
      weight: 110,
      color: 'White',
      pattern: 'Plain',
      printingSide: 'None',
      finish: 'Natural',
      quantity: 100,
      notes: 'Standard back endpaper'
    }
  ]);

  const [editingId, setEditingId] = useState<string | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newOption, setNewOption] = useState<Partial<EndpaperOption>>({});
  const inputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});
  const tapTimeouts = useRef<{ [key: string]: NodeJS.Timeout }>({});

  // Double-tap to edit functionality
  const handleDoubleClick = (optionId: string, field: string) => {
    if (editingId !== optionId) {
      setEditingId(optionId);
      setTimeout(() => {
        const inputKey = `${optionId}-${field}`;
        if (inputRefs.current[inputKey]) {
          inputRefs.current[inputKey]?.focus();
        }
      }, 100);
    }
  };

  const handleInputChange = (optionId: string, field: keyof EndpaperOption, value: string | number) => {
    setEndpaperOptions(prev => 
      prev.map(option => 
        option.id === optionId 
          ? { ...option, [field]: value }
          : option
      )
    );
  };

  const handleSaveEdit = (optionId: string) => {
    setEditingId(null);
  };

  const handleCancelEdit = (optionId: string) => {
    setEditingId(null);
    // Reset to original values if needed
  };

  const handleDeleteOption = (optionId: string) => {
    setEndpaperOptions(prev => prev.filter(option => option.id !== optionId));
  };

  const handleAddNew = () => {
    setIsAddingNew(true);
    setNewOption({
      name: '',
      paperType: '',
      weight: 120,
      color: '',
      pattern: '',
      printingSide: '',
      finish: '',
      quantity: 100,
      notes: ''
    });
  };

  const handleSaveNew = () => {
    if (newOption.name && newOption.paperType) {
      const newId = Date.now().toString();
      setEndpaperOptions(prev => [...prev, {
        id: newId,
        name: newOption.name || '',
        paperType: newOption.paperType || '',
        weight: newOption.weight || 120,
        color: newOption.color || '',
        pattern: newOption.pattern || '',
        printingSide: newOption.printingSide || '',
        finish: newOption.finish || '',
        quantity: newOption.quantity || 100,
        notes: newOption.notes || ''
      }]);
      setIsAddingNew(false);
      setNewOption({});
    }
  };

  const handleCancelNew = () => {
    setIsAddingNew(false);
    setNewOption({});
  };

  const renderEditableField = (option: EndpaperOption, field: keyof EndpaperOption, type: 'text' | 'number' | 'select', selectOptions?: string[]) => {
    const isEditing = editingId === option.id;
    const inputKey = `${option.id}-${field}`;
    
    if (isEditing) {
      if (type === 'select' && selectOptions) {
        return (
          <Select 
            value={option[field] as string} 
            onValueChange={(value) => handleInputChange(option.id, field, value)}
          >
            <SelectTrigger className="h-8 text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {selectOptions.map(opt => (
                <SelectItem key={opt} value={opt}>{opt}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }
      
      return (
        <Input
          ref={(el) => { inputRefs.current[inputKey] = el; }}
          type={type}
          value={option[field] as string | number}
          onChange={(e) => handleInputChange(option.id, field, type === 'number' ? Number(e.target.value) : e.target.value)}
          className="h-8 text-sm"
          onKeyDown={(e) => {
            if (e.key === 'Enter') handleSaveEdit(option.id);
            if (e.key === 'Escape') handleCancelEdit(option.id);
          }}
        />
      );
    }
    
    return (
      <div 
        className="p-2 rounded border border-transparent hover:border-gray-300 dark:hover:border-gray-600 cursor-pointer transition-colors min-h-[32px] flex items-center"
        onDoubleClick={() => handleDoubleClick(option.id, field)}
        title="Double-click to edit"
      >
        <span className="text-sm">{option[field]}</span>
      </div>
    );
  };

  const t = translations[language] || translations.en;

  return (
    <div className="space-y-6">
      <Card className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-shadow duration-300" data-glow>
        <CardHeader className="pt-1 pb-1">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
              Endpaper Paper Options
            </CardTitle>
            <Button 
              onClick={handleAddNew}
              size="sm"
              className="h-8 px-3 text-xs"
              disabled={isAddingNew}
            >
              <Plus className="w-3 h-3 mr-1" />
              Add New
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-4">
            {/* Header Row */}
            <div className="grid grid-cols-12 gap-2 text-xs font-medium text-gray-600 dark:text-gray-400 border-b pb-2">
              <div className="col-span-2">Name</div>
              <div className="col-span-1">Type</div>
              <div className="col-span-1">Weight</div>
              <div className="col-span-1">Color</div>
              <div className="col-span-1">Pattern</div>
              <div className="col-span-1">Printing</div>
              <div className="col-span-1">Finish</div>
              <div className="col-span-1">Qty</div>
              <div className="col-span-2">Notes</div>
              <div className="col-span-1">Actions</div>
            </div>

            {/* Existing Options */}
            {endpaperOptions.map((option) => (
              <div key={option.id} className="grid grid-cols-12 gap-2 items-center py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                <div className="col-span-2">
                  {renderEditableField(option, 'name', 'text')}
                </div>
                <div className="col-span-1">
                  {renderEditableField(option, 'paperType', 'select', ['Cotton Paper', 'Textured Paper', 'Linen Paper', 'Kraft Paper', 'Handmade Paper'])}
                </div>
                <div className="col-span-1">
                  {renderEditableField(option, 'weight', 'number')}
                </div>
                <div className="col-span-1">
                  {renderEditableField(option, 'color', 'select', ['White', 'Cream', 'Natural', 'Gray', 'Black', 'Custom'])}
                </div>
                <div className="col-span-1">
                  {renderEditableField(option, 'pattern', 'select', ['Plain', 'Marbled', 'Textured', 'Geometric', 'Floral', 'Custom'])}
                </div>
                <div className="col-span-1">
                  {renderEditableField(option, 'printingSide', 'select', ['None', 'Single', 'Double'])}
                </div>
                <div className="col-span-1">
                  {renderEditableField(option, 'finish', 'select', ['Matte', 'Glossy', 'Natural', 'Textured'])}
                </div>
                <div className="col-span-1">
                  {renderEditableField(option, 'quantity', 'number')}
                </div>
                <div className="col-span-2">
                  {renderEditableField(option, 'notes', 'text')}
                </div>
                <div className="col-span-1 flex gap-1 items-center">
                  {editingId === option.id ? (
                    <>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={() => handleSaveEdit(option.id)}
                      >
                        <Check className="w-3 h-3 text-green-600" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={() => handleCancelEdit(option.id)}
                      >
                        <X className="w-3 h-3 text-red-600" />
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={() => setEditingId(option.id)}
                      >
                        <Edit3 className="w-3 h-3 text-blue-600" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={() => handleDeleteOption(option.id)}
                      >
                        <Trash2 className="w-3 h-3 text-red-600" />
                      </Button>
                    </>
                  )}
                </div>
              </div>
            ))}

            {/* Add New Row */
            {isAddingNew && (
              <div className="grid grid-cols-12 gap-2 items-center py-2 border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                <div className="col-span-2">
                  <Input
                    placeholder="Name"
                    value={newOption.name || ''}
                    onChange={(e) => setNewOption(prev => ({ ...prev, name: e.target.value }))}
                    className="h-8 text-sm"
                  />
                </div>
                <div className="col-span-1">
                  <Select value={newOption.paperType || ''} onValueChange={(value) => setNewOption(prev => ({ ...prev, paperType: value }))}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Cotton Paper">Cotton Paper</SelectItem>
                      <SelectItem value="Textured Paper">Textured Paper</SelectItem>
                      <SelectItem value="Linen Paper">Linen Paper</SelectItem>
                      <SelectItem value="Kraft Paper">Kraft Paper</SelectItem>
                      <SelectItem value="Handmade Paper">Handmade Paper</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-1">
                  <Input
                    type="number"
                    placeholder="120"
                    value={newOption.weight || ''}
                    onChange={(e) => setNewOption(prev => ({ ...prev, weight: Number(e.target.value) }))}
                    className="h-8 text-sm"
                  />
                </div>
                <div className="col-span-1">
                  <Select value={newOption.color || ''} onValueChange={(value) => setNewOption(prev => ({ ...prev, color: value }))}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="Color" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="White">White</SelectItem>
                      <SelectItem value="Cream">Cream</SelectItem>
                      <SelectItem value="Natural">Natural</SelectItem>
                      <SelectItem value="Gray">Gray</SelectItem>
                      <SelectItem value="Black">Black</SelectItem>
                      <SelectItem value="Custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-1">
                  <Select value={newOption.pattern || ''} onValueChange={(value) => setNewOption(prev => ({ ...prev, pattern: value }))}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="Pattern" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Plain">Plain</SelectItem>
                      <SelectItem value="Marbled">Marbled</SelectItem>
                      <SelectItem value="Textured">Textured</SelectItem>
                      <SelectItem value="Geometric">Geometric</SelectItem>
                      <SelectItem value="Floral">Floral</SelectItem>
                      <SelectItem value="Custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-1">
                  <Select value={newOption.printingSide || ''} onValueChange={(value) => setNewOption(prev => ({ ...prev, printingSide: value }))}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="Print" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="None">None</SelectItem>
                      <SelectItem value="Single">Single</SelectItem>
                      <SelectItem value="Double">Double</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-1">
                  <Select value={newOption.finish || ''} onValueChange={(value) => setNewOption(prev => ({ ...prev, finish: value }))}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="Finish" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Matte">Matte</SelectItem>
                      <SelectItem value="Glossy">Glossy</SelectItem>
                      <SelectItem value="Natural">Natural</SelectItem>
                      <SelectItem value="Textured">Textured</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-1">
                  <Input
                    type="number"
                    placeholder="100"
                    value={newOption.quantity || ''}
                    onChange={(e) => setNewOption(prev => ({ ...prev, quantity: Number(e.target.value) }))}
                    className="h-8 text-sm"
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    placeholder="Notes"
                    value={newOption.notes || ''}
                    onChange={(e) => setNewOption(prev => ({ ...prev, notes: e.target.value }))}
                    className="h-8 text-sm"
                  />
                </div>
                <div className="col-span-1 flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    onClick={handleSaveNew}
                  >
                    <Check className="w-3 h-3 text-green-600" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    onClick={handleCancelNew}
                  >
                    <X className="w-3 h-3 text-red-600" />
                  </Button>
                </div>
              </div>
            )}
          </div>

          {endpaperOptions.length === 0 && !isAddingNew && (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>No endpaper options configured.</p>
              <p className="text-sm mt-1">Click "Add New" to create your first endpaper option.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions Card */}
      <Card className="bg-blue-50/90 dark:bg-blue-900/20 backdrop-blur-sm border border-blue-200 dark:border-blue-700 shadow-lg" data-glow>
        <CardContent className="pt-6">
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <h4 className="font-medium mb-2">💡 Usage Tips:</h4>
            <ul className="space-y-1 text-xs">
              <li>• <strong>Double-click</strong> any field to edit it inline</li>
              <li>• <strong>Paper Weight:</strong> Typically 100-130 GSM for endpapers <mcreference link="https://www.paper-world.com/en/newsdetail/endpaper-uses-types-and-benefits-bookbinding" index="1">1</mcreference></li>
              <li>• <strong>Patterns:</strong> Marbled and textured papers add decorative value <mcreference link="https://www.paper-world.com/en/newsdetail/endpaper-uses-types-and-benefits-bookbinding" index="1">1</mcreference></li>
              <li>• <strong>Printing:</strong> Consider single-sided for cost efficiency <mcreference link="https://www.book-printing-factory.com/products-news/endpaper-printing-an-essential-element-of-bookbinding.html" index="2">2</mcreference></li>
              <li>• <strong>Cotton Paper:</strong> Provides better durability and tear resistance <mcreference link="https://www.paper-world.com/en/newsdetail/endpaper-uses-types-and-benefits-bookbinding" index="1">1</mcreference></li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EndpaperOptionsCard;