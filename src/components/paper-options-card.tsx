'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Trash2, Edit, Plus, Save, X } from 'lucide-react';

export interface PaperOption {
  id: string;
  name: string;
  paperType: string;
  weight: number;
  color: string;
  finish: string;
  printingSide: string;
  quantity: number;
  notes: string;
  [key: string]: any; // Allow additional fields
}

export interface PaperOptionsConfig {
  title: string;
  paperTypes: string[];
  finishes: string[];
  printingSides: string[];
  additionalFields?: {
    [key: string]: {
      label: string;
      type: 'select' | 'input' | 'number';
      options?: string[];
      defaultValue?: any;
    };
  };
  defaultValues: Partial<PaperOption>;
  initialData: PaperOption[];
}

interface PaperOptionsCardProps {
  config: PaperOptionsConfig;
  onDataChange?: (data: PaperOption[]) => void;
}

const PaperOptionsCard: React.FC<PaperOptionsCardProps> = ({ config, onDataChange }) => {
  const [options, setOptions] = useState<PaperOption[]>(config.initialData);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState<string>('');
  const [isAdding, setIsAdding] = useState(false);
  const [newOption, setNewOption] = useState<Partial<PaperOption>>(config.defaultValues);

  const updateOptions = (newOptions: PaperOption[]) => {
    setOptions(newOptions);
    onDataChange?.(newOptions);
  };

  const handleDoubleClick = (id: string, field: string, value: string | number) => {
    setEditingId(id);
    setEditingField(field);
    setTempValue(value.toString());
  };

  const handleSave = () => {
    if (editingId && editingField) {
      updateOptions(options.map(option => {
        if (option.id === editingId) {
          const updatedOption = { ...option };
          if (editingField === 'weight' || editingField === 'quantity') {
            (updatedOption as any)[editingField] = parseInt(tempValue) || 0;
          } else {
            (updatedOption as any)[editingField] = tempValue;
          }
          return updatedOption;
        }
        return option;
      }));
    }
    setEditingId(null);
    setEditingField(null);
    setTempValue('');
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditingField(null);
    setTempValue('');
  };

  const handleDelete = (id: string) => {
    updateOptions(options.filter(option => option.id !== id));
  };

  const handleAddNew = () => {
    if (newOption.name) {
      const id = Date.now().toString();
      updateOptions([...options, { ...newOption, id } as PaperOption]);
      setNewOption(config.defaultValues);
      setIsAdding(false);
    }
  };

  const renderEditableField = (option: PaperOption, field: string, label: string) => {
    const isEditing = editingId === option.id && editingField === field;
    const value = option[field];

    if (isEditing) {
      // Handle select fields
      if (field === 'paperType') {
        return (
          <Select value={tempValue} onValueChange={setTempValue}>
            <SelectTrigger className="h-8 text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400" data-glow>
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
              {config.paperTypes.map(type => (
                <SelectItem key={type} value={type} className="text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 focus:bg-blue-100 dark:focus:bg-blue-900">{type}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }
      
      if (field === 'finish') {
        return (
          <Select value={tempValue} onValueChange={setTempValue}>
            <SelectTrigger className="h-8 text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400" data-glow>
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
              {config.finishes.map(finish => (
                <SelectItem key={finish} value={finish} className="text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 focus:bg-blue-100 dark:focus:bg-blue-900">{finish}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }
      
      if (field === 'printingSide') {
        return (
          <Select value={tempValue} onValueChange={setTempValue}>
            <SelectTrigger className="h-8 text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400" data-glow>
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
              {config.printingSides.map(side => (
                <SelectItem key={side} value={side} className="text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 focus:bg-blue-100 dark:focus:bg-blue-900">{side}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }

      // Handle additional fields
      if (config.additionalFields?.[field]) {
        const fieldConfig = config.additionalFields[field];
        if (fieldConfig.type === 'select' && fieldConfig.options) {
          return (
            <Select value={tempValue} onValueChange={setTempValue}>
              <SelectTrigger className="h-8 text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400" data-glow>
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
                {fieldConfig.options.map(option => (
                  <SelectItem key={option} value={option} className="text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 focus:bg-blue-100 dark:focus:bg-blue-900">{option}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          );
        }
      }
      
      if (field === 'notes') {
        return (
          <Textarea
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            className="min-h-[60px] text-sm resize-none border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
            data-glow
          />
        );
      }
      
      return (
        <Input
          value={tempValue}
          onChange={(e) => setTempValue(e.target.value)}
          className="h-8 text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
          type={field === 'weight' || field === 'quantity' ? 'number' : 'text'}
          onKeyDown={(e) => {
            if (e.key === 'Enter') handleSave();
            if (e.key === 'Escape') handleCancel();
          }}
          data-glow
        />
      );
    }

    return (
      <div 
        className="editable-field cursor-pointer px-2 py-1 rounded text-sm min-h-[24px] flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 border border-transparent hover:border-blue-200 dark:hover:border-blue-700"
        onDoubleClick={() => handleDoubleClick(option.id, field, value)}
        title="Double-click to edit"
      >
        {field === 'notes' ? (
          <div className="max-h-16 overflow-y-auto text-xs text-gray-600 dark:text-gray-400 w-full">
            {value || <span className="italic text-gray-400 dark:text-gray-500">No notes</span>}
          </div>
        ) : (
          <span className="truncate text-gray-900 dark:text-gray-100">{value}</span>
        )}
      </div>
    );
  };

  const renderNewOptionField = (field: string, label: string) => {
    if (field === 'paperType') {
      return (
        <Select
          value={newOption.paperType}
          onValueChange={(value) => setNewOption(prev => ({ ...prev, paperType: value }))}
        >
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {config.paperTypes.map(type => (
              <SelectItem key={type} value={type}>{type}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    if (field === 'finish') {
      return (
        <Select
          value={newOption.finish}
          onValueChange={(value) => setNewOption(prev => ({ ...prev, finish: value }))}
        >
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {config.finishes.map(finish => (
              <SelectItem key={finish} value={finish}>{finish}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    if (field === 'printingSide') {
      return (
        <Select
          value={newOption.printingSide}
          onValueChange={(value) => setNewOption(prev => ({ ...prev, printingSide: value }))}
        >
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {config.printingSides.map(side => (
              <SelectItem key={side} value={side}>{side}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    if (config.additionalFields?.[field]) {
      const fieldConfig = config.additionalFields[field];
      if (fieldConfig.type === 'select' && fieldConfig.options) {
        return (
          <Select
            value={newOption[field]}
            onValueChange={(value) => setNewOption(prev => ({ ...prev, [field]: value }))}
          >
            <SelectTrigger className="h-8 text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400" data-glow>
          <SelectValue placeholder={label} />
        </SelectTrigger>
            <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
              {fieldConfig.options.map(option => (
                <SelectItem 
                  key={option} 
                  value={option}
                  className="text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 focus:bg-blue-100 dark:focus:bg-blue-900"
                >
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }
    }

    if (field === 'notes') {
      return (
        <Textarea
        placeholder={label}
        value={newOption[field] || ''}
        onChange={(e) => setNewOption(prev => ({ ...prev, [field]: e.target.value }))}
        className="h-8 text-sm resize-none border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
        rows={1}
        data-glow
      />
      );
    }

    return (
      <Input
        placeholder={label}
        value={newOption[field] || ''}
        onChange={(e) => {
          const value = field === 'weight' || field === 'quantity' 
            ? parseInt(e.target.value) || 0 
            : e.target.value;
          setNewOption(prev => ({ ...prev, [field]: value }));
        }}
        className="h-8 text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
        type={field === 'weight' || field === 'quantity' ? 'number' : 'text'}
        data-glow
      />
    );
  };

  // Fixed 10-column grid structure for all paper types
  const baseFields = ['name', 'paperType', 'color', 'finish', 'printingSide', 'quantity', 'notes', 'weight', 'costTon'];
  const additionalFieldKeys = Object.keys(config.additionalFields || {});
  const gridCols = 10; // Fixed 10 columns: 9 data columns + 1 actions column

  const getFieldLabel = (field: string) => {
    const labels: { [key: string]: string } = {
      name: 'Paper Name',
      paperType: 'Source',
      color: 'Sheet H (mm)',
      finish: 'Sheet W (mm)',
      printingSide: 'Grain || To',
      quantity: 'Caliper (µm)',
      notes: 'Cost/Ream ($)',
      weight: 'GSM (g/m²)',
      costTon: 'Cost/Ton ($)'
    };
    return config.additionalFields?.[field]?.label || labels[field] || field;
  };

  return (
    <Card className="w-full bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-shadow duration-300" data-glow>
      <CardHeader className="pt-1 pb-1">
        <CardTitle className="flex items-center justify-between text-lg font-semibold text-gray-900 dark:text-gray-100">
          {config.title}
          <Button
            onClick={() => setIsAdding(true)}
            size="sm"
            className="flex items-center gap-2 h-8 px-3 text-xs bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors duration-200"
            disabled={isAdding}
            data-glow
          >
            <Plus className="h-3 w-3" />
            Add New
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Enter full original sheet dimensions. Sheets &gt; 720x1020mm will be rotated/trimmed.
          </p>
          {/* Header Row */}
          <div className="mt-4 grid grid-cols-10 gap-0 text-xs font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 border border-purple-300 dark:border-purple-600 rounded-t-lg">
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Paper Name</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Source</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Sheet H (mm)</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Sheet W (mm)</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Grain || To</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Caliper (µm)</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Cost/Ream ($)</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">GSM (g/m²)</div>
            <div className="col-span-1 px-3 py-3 border-r border-purple-300 dark:border-purple-600 font-semibold">Cost/Ton ($)</div>
            <div className="col-span-1 px-3 py-3 font-semibold">Actions</div>
          </div>
          
          {/* Existing Options */}
          <div className="mt-0 border-l border-r border-b border-purple-300 dark:border-purple-600 rounded-b-lg overflow-hidden">
            {options.map((option, index) => (
              <div key={option.id} className={`grid grid-cols-10 gap-0 items-center hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-all duration-200 ${index !== options.length - 1 ? 'border-b border-purple-200 dark:border-purple-700' : ''}`}>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'name', getFieldLabel('name'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'paperType', getFieldLabel('paperType'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'color', getFieldLabel('color'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'finish', getFieldLabel('finish'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'printingSide', getFieldLabel('printingSide'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'quantity', getFieldLabel('quantity'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'notes', getFieldLabel('notes'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderEditableField(option, 'weight', getFieldLabel('weight'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  <div className="editable-field cursor-pointer px-2 py-1 rounded text-sm min-h-[24px] flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 border border-transparent hover:border-blue-200 dark:hover:border-blue-700">
                    <span className="truncate text-gray-900 dark:text-gray-100">-</span>
                  </div>
                </div>
                <div className="col-span-1 px-3 py-3 flex gap-1 justify-center">
                {editingId === option.id ? (
                  <>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 hover:bg-green-100 dark:hover:bg-green-900/20"
                      onClick={handleSave}
                      data-glow
                    >
                      <Save className="w-3 h-3 text-green-600 dark:text-green-400" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-900/20"
                      onClick={handleCancel}
                      data-glow
                    >
                      <X className="w-3 h-3 text-red-600 dark:text-red-400" />
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/20"
                      onClick={() => handleDoubleClick(option.id, 'name', option.name)}
                      data-glow
                    >
                      <Edit className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-900/20"
                      onClick={() => handleDelete(option.id)}
                      data-glow
                    >
                      <Trash2 className="w-3 h-3 text-red-600 dark:text-red-400" />
                    </Button>
                  </>
                )}
                </div>
              </div>
            ))}
          </div>
          
          {/* Add New Row */}
          {isAdding && (
            <div className="mt-4 border border-purple-300 dark:border-purple-600 bg-purple-50 dark:bg-purple-900/10 rounded-lg overflow-hidden animate-fadeIn">
              <div className="grid grid-cols-10 gap-0 items-center">
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('name', getFieldLabel('name'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('paperType', getFieldLabel('paperType'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('color', getFieldLabel('color'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('finish', getFieldLabel('finish'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('printingSide', getFieldLabel('printingSide'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('quantity', getFieldLabel('quantity'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('notes', getFieldLabel('notes'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  {renderNewOptionField('weight', getFieldLabel('weight'))}
                </div>
                <div className="col-span-1 px-3 py-3 border-r border-purple-200 dark:border-purple-700">
                  <Input
                    placeholder="Cost/Ton"
                    className="h-8 text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                    disabled
                    data-glow
                  />
                </div>
                <div className="col-span-1 px-3 py-3 flex gap-1 justify-center">
                <Button 
                  size="sm" 
                  onClick={handleAddNew} 
                  className="h-6 w-6 p-0 bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600"
                  disabled={!newOption.name}
                >
                  <Save className="h-3 w-3" />
                </Button>
                <Button 
                   size="sm" 
                   variant="outline" 
                   onClick={() => setIsAdding(false)} 
                   className="h-6 w-6 p-0 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-800"
                 >
                   <X className="h-3 w-3" />
                 </Button>
               </div>
             </div>
           </div>
           )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PaperOptionsCard;