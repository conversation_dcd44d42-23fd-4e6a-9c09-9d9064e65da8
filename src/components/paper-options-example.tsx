// Example Usage of Paper Options with Database Storage
// This file demonstrates how to use the paper database storage system
// with the existing PaperOptionsCard components

'use client';

import React from 'react';
import PaperOptionsCard from './paper-options-card';
import { usePaperData, usePaperStats } from '@/lib/hooks/use-paper-data';
import { innerTextConfig, coverConfig, endpaperConfig } from '@/lib/paper-configs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, Download, Upload, Trash2 } from 'lucide-react';

// Individual Paper Category Components
export const InnerTextPaperCard: React.FC = () => {
  const {
    papers,
    loading,
    error,
    addPaper,
    updatePaper,
    deletePaper,
    resetToDefaults,
    reload,
    clearError
  } = usePaperData('Inner Text');

  // Update the config with current data
  const currentConfig = {
    ...innerTextConfig,
    initialData: papers
  };

  const handleDataChange = (newData: any[]) => {
    // The hook automatically handles data persistence
    // This callback can be used for additional side effects if needed
    console.log('Inner text papers updated:', newData.length, 'papers');
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading inner text papers...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-red-200">
        <CardContent className="p-6">
          <div className="text-red-600">
            <p>Error loading inner text papers: {error}</p>
            <div className="mt-4 space-x-2">
              <Button onClick={reload} size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Button onClick={clearError} variant="outline" size="sm">
                Clear Error
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-end space-x-2">
        <Button onClick={resetToDefaults} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Reset to Defaults
        </Button>
      </div>
      <PaperOptionsCard 
        config={currentConfig} 
        onDataChange={handleDataChange}
      />
    </div>
  );
};

export const CoverPaperCard: React.FC = () => {
  const {
    papers,
    loading,
    error,
    resetToDefaults,
    reload,
    clearError
  } = usePaperData('Cover');

  const currentConfig = {
    ...coverConfig,
    initialData: papers
  };

  const handleDataChange = (newData: any[]) => {
    console.log('Cover papers updated:', newData.length, 'papers');
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading cover papers...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-red-200">
        <CardContent className="p-6">
          <div className="text-red-600">
            <p>Error loading cover papers: {error}</p>
            <div className="mt-4 space-x-2">
              <Button onClick={reload} size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Button onClick={clearError} variant="outline" size="sm">
                Clear Error
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-end space-x-2">
        <Button onClick={resetToDefaults} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Reset to Defaults
        </Button>
      </div>
      <PaperOptionsCard 
        config={currentConfig} 
        onDataChange={handleDataChange}
      />
    </div>
  );
};

export const EndpaperCard: React.FC = () => {
  const {
    papers,
    loading,
    error,
    resetToDefaults,
    reload,
    clearError
  } = usePaperData('Endpapers');

  const currentConfig = {
    ...endpaperConfig,
    initialData: papers
  };

  const handleDataChange = (newData: any[]) => {
    console.log('Endpapers updated:', newData.length, 'papers');
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading endpapers...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-red-200">
        <CardContent className="p-6">
          <div className="text-red-600">
            <p>Error loading endpapers: {error}</p>
            <div className="mt-4 space-x-2">
              <Button onClick={reload} size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Button onClick={clearError} variant="outline" size="sm">
                Clear Error
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-end space-x-2">
        <Button onClick={resetToDefaults} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Reset to Defaults
        </Button>
      </div>
      <PaperOptionsCard 
        config={currentConfig} 
        onDataChange={handleDataChange}
      />
    </div>
  );
};

// Statistics Dashboard Component
export const PaperStatsDashboard: React.FC = () => {
  const { stats, refresh } = usePaperStats();

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Paper Database Statistics
          <Button onClick={refresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.totalPapers}</div>
            <div className="text-sm text-gray-600">Total Papers</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.averageWeight}g</div>
            <div className="text-sm text-gray-600">Average Weight</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {stats.weightRange.min}-{stats.weightRange.max}g
            </div>
            <div className="text-sm text-gray-600">Weight Range</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {Object.keys(stats.categoryCounts).length}
            </div>
            <div className="text-sm text-gray-600">Categories</div>
          </div>
        </div>
        
        <div className="mt-6">
          <h4 className="font-semibold mb-2">Papers by Category:</h4>
          <div className="space-y-1">
            {Object.entries(stats.categoryCounts).map(([category, count]) => (
              <div key={category} className="flex justify-between text-sm">
                <span>{category}:</span>
                <span className="font-medium">{count} papers</span>
              </div>
            ))}
          </div>
        </div>
        
        <div className="mt-4">
          <h4 className="font-semibold mb-2">Papers by Type:</h4>
          <div className="space-y-1">
            {Object.entries(stats.paperTypeCounts).map(([type, count]) => (
              <div key={type} className="flex justify-between text-sm">
                <span>{type}:</span>
                <span className="font-medium">{count} papers</span>
              </div>
            ))}
          </div>
        </div>
        
        {stats.lastUpdated && (
          <div className="mt-4 text-xs text-gray-500">
            Last updated: {stats.lastUpdated.toLocaleString()}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Complete Paper Management Dashboard
export const PaperManagementDashboard: React.FC = () => {
  const { 
    allData, 
    loading, 
    error, 
    importData, 
    resetAllCategories, 
    clearAllData 
  } = usePaperData('Inner Text'); // We can use any category hook for global operations

  const handleExportData = () => {
    const dataStr = JSON.stringify(allData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `paper-options-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target?.result as string);
          importData(data);
        } catch (err) {
          console.error('Failed to parse import file:', err);
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <div className="space-y-6">
      {/* Management Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Paper Database Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button onClick={handleExportData}>
              <Download className="h-4 w-4 mr-2" />
              Export All Data
            </Button>
            
            <label className="cursor-pointer">
              <Button variant="outline" asChild>
                <span>
                  <Upload className="h-4 w-4 mr-2" />
                  Import Data
                </span>
              </Button>
              <input
                type="file"
                accept=".json"
                onChange={handleImportData}
                className="hidden"
              />
            </label>
            
            <Button onClick={resetAllCategories} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset All to Defaults
            </Button>
            
            <Button onClick={clearAllData} variant="destructive">
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All Data
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Dashboard */}
      <PaperStatsDashboard />

      {/* Paper Category Cards */}
      <div className="space-y-8">
        <InnerTextPaperCard />
        <CoverPaperCard />
        <EndpaperCard />
      </div>
    </div>
  );
};

// Export the main dashboard as default
export default PaperManagementDashboard;