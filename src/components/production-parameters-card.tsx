"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface ProductionParametersProps {
  className?: string;
}

// Custom Toggle Switch Component
interface ToggleSwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  variant?: "default" | "green-red";
  className?: string;
}

function ToggleSwitch({ checked, onChange, label, variant = "default", className = "" }: ToggleSwitchProps) {
  const isGreenRedAligned = variant === "green-red" && !checked; // Green when 'Aligned' (unchecked)
  const isGreenRedMisaligned = variant === "green-red" && checked; // Red when 'Misaligned' (checked)
  const isDefaultUncheckedGreen = variant === "default" && !checked && (label === "x1" || label === "Aligned"); // Specifically for x1 and Aligned default to green when unchecked

  const getTrackClasses = () => {
    if (variant === "green-red") {
      return checked // Misaligned (right, checked)
        ? "bg-gradient-to-r from-red-400 to-pink-500 shadow-md shadow-pink-500/30" // Red track
        : "bg-gradient-to-r from-green-400 to-emerald-500 shadow-md shadow-emerald-500/30"; // Green track for Aligned (left, unchecked)
    }
    // Default variant: x1 (unchecked) should be green
    if (isDefaultUncheckedGreen) {
      return "bg-gradient-to-r from-green-400 to-emerald-500 shadow-md shadow-emerald-500/30"; // Green track
    }
    // Default variant, other states
    return checked
      ? "bg-gradient-to-r from-sky-400 to-blue-500 shadow-md shadow-blue-500/30"
      : "bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-500 dark:to-gray-600 shadow-sm";
  };

  const getLabelClasses = () => {
    if (variant === "green-red") {
      return checked ? "text-red-600 dark:text-red-400" : "text-emerald-600 dark:text-emerald-400";
    }
    if (isDefaultUncheckedGreen) {
      return "text-emerald-600 dark:text-emerald-400";
    }
    return checked ? "text-blue-600 dark:text-blue-400" : "text-gray-700 dark:text-gray-300";
  };

  const getFocusRingClasses = () => {
    if (isGreenRedAligned || isDefaultUncheckedGreen) return "focus:ring-emerald-400";
    if (isGreenRedMisaligned) return "focus:ring-pink-400";
    if (checked) return "focus:ring-blue-400";
    return "focus:ring-gray-400";
  };

  return (
    <div className={`flex items-center gap-2.5 ${className}`}>
      <button
        type="button"
        onClick={() => onChange(!checked)}
        className={`relative inline-flex h-7 w-12 items-center rounded-full transition-colors duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-900 ${getTrackClasses()} ${getFocusRingClasses()}`}
        aria-pressed={checked}
      >
        <span className="sr-only">{label}</span>
        <span
          className="inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-1 ring-gray-900/5 transition-transform duration-300 ease-in-out"
          style={{
            transform: checked ? 'translateX(25px)' : 'translateX(2px)',
            // Ensure transition property is explicitly set for transform
            transitionProperty: 'transform',
          }}
        >
          <span className={`absolute inset-px rounded-full transition-opacity duration-200 ${checked ? 'opacity-100' : 'opacity-0'} ${isGreenRedMisaligned ? 'bg-gradient-to-br from-red-50 to-red-100' : (isGreenRedAligned || isDefaultUncheckedGreen ? 'bg-gradient-to-br from-green-50 to-green-100' : 'bg-gradient-to-br from-white to-gray-50') }`}></span>
        </span>
      </button>
      {label && (
        <span className={`text-sm font-medium select-none transition-colors duration-300 ${getLabelClasses()}`}>
          {label}
        </span>
      )}
    </div>
  );
}

export default function ProductionParametersCard({ className = "" }: ProductionParametersProps) {
  const [bleed, setBleed] = useState("3");
  const [gripper, setGripper] = useState("12");
  const [colorBar, setColorBar] = useState("6");
  const [sideLip, setSideLip] = useState("5");
  const [sideLipMultiplier, setSideLipMultiplier] = useState(true); // true = x1, false = x2
  const [grainAlignment, setGrainAlignment] = useState(true); // true = aligned, false = misaligned

  return (
    <Card className={`bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-shadow duration-300 ${className}`} data-glow>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
          Production Parameters
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Bleed and Gripper Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="bleed" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Bleed
              </Label>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                mm
              </span>
            </div>
            <Input
              id="bleed"
              type="number"
              placeholder="3"
              value={bleed}
              onChange={(e) => setBleed(e.target.value)}
              className="h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              data-glow
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="gripper" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Gripper
              </Label>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                mm
              </span>
            </div>
            <Input
              id="gripper"
              type="number"
              placeholder="12"
              value={gripper}
              onChange={(e) => setGripper(e.target.value)}
              className="h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              data-glow
            />
          </div>
        </div>

        {/* Color Bar and Side Lip Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="color-bar" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Color Bar
              </Label>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                mm
              </span>
            </div>
            <Input
              id="color-bar"
              type="number"
              placeholder="6"
              value={colorBar}
              onChange={(e) => setColorBar(e.target.value)}
              className="h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              data-glow
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label htmlFor="side-lip" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Side Lip
              </Label>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                mm
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Input
                id="side-lip"
                type="number"
                placeholder="5"
                value={sideLip}
                onChange={(e) => setSideLip(e.target.value)}
                className="h-9 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 flex-1"
                data-glow
              />
              <ToggleSwitch
                checked={sideLipMultiplier}
                onChange={setSideLipMultiplier}
                label={sideLipMultiplier ? "x1" : "x2"}
                className="flex-shrink-0"
              />
            </div>
          </div>
        </div>

        {/* Grain Alignment */}
        <div>
          <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">
            Grain Alignment
          </Label>
          <ToggleSwitch
            checked={grainAlignment}
            onChange={setGrainAlignment}
            label={grainAlignment ? "Aligned" : "Misaligned"}
            variant="green-red"
          />
        </div>

        {/* Footer Note */}
        <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            * Gutter not used for sheet area. Margins applied to 720H x 1020W press.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}